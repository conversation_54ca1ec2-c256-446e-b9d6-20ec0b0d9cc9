{"name": "FreeSans", "type": "TTF", "desc": {"CapHeight": 729, "XHeight": 524, "FontBBox": "[-1166 -638 2260 1050]", "Flags": 4, "Ascent": 900, "Descent": -300, "Leading": 0, "ItalicAngle": 0, "StemV": 87, "MissingWidth": 800}, "unitsPerEm": 1000, "up": -176, "ut": 50, "strp": 258, "strs": 49, "ttffile": "/usr/local/www/diis/libs/vendor/mpdf/mpdf/src/Config/../../ttfonts/FreeSans.ttf", "TTCfontID": 0, "originalsize": 1563256, "sip": false, "smp": true, "BMPselected": false, "fontkey": "freesans", "panose": " 8 5 2 b 5 4 2 2 2 2 2 4", "haskerninfo": true, "haskernGPOS": true, "hassmallcapsGSUB": true, "fontmetrics": "win", "useOTL": 255, "rtlPUAstr": "", "GSUBScriptLang": {"DFLT": "DFLT ", "armn": "DFLT ", "beng": "DFLT ", "bng2": "DFLT ", "cyrl": "DFLT MKD  SRB  ", "dev2": "DFLT SAN  ", "deva": "DFLT SAN  ", "grek": "DFLT ", "gur2": "DFLT ", "guru": "DFLT ", "hebr": "DFLT IWR  JII  ", "latn": "DFLT CAT  ISM  LSM  NLD  NSM  SKS  TRK  ", "mlm2": "DFLT ", "mlym": "DFLT "}, "GSUBFeatures": {"DFLT": {"DFLT": {"frac": [14], "pnum": [15], "zero": [16]}}, "armn": {"DFLT": {"dlig": [22], "hlig": [23]}}, "beng": {"DFLT": {"init": [36], "akhn": [37], "rphf": [38], "blwf": [39], "half": [40], "vatu": [41], "pstf": [42], "pres": [43], "blws": [44], "psts": [45], "haln": [46], "aalt": [47]}}, "bng2": {"DFLT": {"init": [36], "akhn": [37], "rphf": [38], "blwf": [39], "half": [40], "vatu": [41], "pstf": [42], "pres": [43], "blws": [44], "psts": [45], "haln": [46], "aalt": [47]}}, "cyrl": {"DFLT": {"pnum": [15], "liga": [18]}, "MKD ": {"locl": [17]}, "SRB ": {"locl": [17]}}, "dev2": {"DFLT": {"nukt": [24], "akhn": [25], "rphf": [26], "blwf": [27], "half": [28], "vatu": [29], "pres": [31], "abvs": [32], "blws": [33], "haln": [34], "locl": [35]}, "SAN ": {"nukt": [24], "akhn": [25], "rphf": [26], "blwf": [27], "half": [28], "vatu": [29], "pres": [30, 31], "abvs": [32], "blws": [33], "haln": [34], "locl": [35]}}, "deva": {"DFLT": {"nukt": [24], "akhn": [25], "rphf": [26], "blwf": [27], "half": [28], "vatu": [29], "pres": [31], "abvs": [32], "blws": [33], "haln": [34], "locl": [35]}, "SAN ": {"nukt": [24], "akhn": [25], "rphf": [26], "blwf": [27], "half": [28], "vatu": [29], "pres": [30, 31], "abvs": [32], "blws": [33], "haln": [34], "locl": [35]}}, "grek": {"DFLT": {"pnum": [15]}}, "gur2": {"DFLT": {"nukt": [48], "blwf": [49], "pstf": [50], "blws": [51], "abvs": [52], "psts": [53]}}, "guru": {"DFLT": {"nukt": [48], "blwf": [49], "pstf": [50], "blws": [51], "abvs": [52], "psts": [53]}}, "hebr": {"DFLT": {"ccmp": [19], "dlig": [21]}, "IWR ": {"ccmp": [19], "dlig": [21]}, "JII ": {"ccmp": [19, 20], "dlig": [21]}}, "latn": {"DFLT": {"ccmp": [0, 2, 5, 6], "smcp": [8], "c2sc": [10], "liga": [11], "hlig": [12], "pnum": [15]}, "CAT ": {"liga": [3]}, "ISM ": {"locl": [13]}, "LSM ": {"locl": [13]}, "NLD ": {"liga": [4]}, "NSM ": {"locl": [13]}, "SKS ": {"locl": [13]}, "TRK ": {"smcp": [7], "c2sc": [9]}}, "mlm2": {"DFLT": {"akhn": [54]}}, "mlym": {"DFLT": {"akhn": [54], "half": [55], "blwf": [56], "blws": [57], "pres": [58], "psts": [59], "haln": [60], "pstf": [61]}}}, "GSUBLookups": [{"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [1516956], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 0, "SubtableCount": 1, "Subtables": [1516988], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1517008], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1517134], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1517172], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1517210, 1517276], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1517506], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1517820], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1517838], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1517970], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1517982], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1518114, 1518190], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1518250], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1518274], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 8, "SubtableCount": 1, "Subtables": [1518286], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1518466], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1518482], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1518494], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1518506], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 265, "SubtableCount": 1, "Subtables": [1518530], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 265, "SubtableCount": 1, "Subtables": [1518792], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 9, "SubtableCount": 1, "Subtables": [1518830], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1518854], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1518916], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1518940], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1519166], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1519208], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1519232], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1519256, 1519320], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 4, "Subtables": [1519914, 1519980, 1520004, 1520084], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1520290], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1520630], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1520900, 1521060], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521208], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521270], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1521434], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1521452], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521466], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521492], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521530], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1521694], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1522166], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1522554], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1522606], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1524096], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1525190], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1525296], "MarkFilteringSet": ""}, {"Type": 1, "Flag": 0, "SubtableCount": 1, "Subtables": [1525768], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1525782], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1526296], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1526348], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1526372], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1526472], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1526526], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 3, "Subtables": [1526578, 1526880, 1528130], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1528236], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1528750], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1528774], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1529120], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1530600], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1536942], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1537048], "MarkFilteringSet": ""}], "GPOSScriptLang": {"DFLT": "DFLT ", "armn": "DFLT ", "beng": "DFLT ", "bng2": "DFLT ", "cyrl": "DFLT ", "dev2": "DFLT ", "deva": "DFLT ", "geor": "DFLT ", "gjr2": "DFLT ", "grek": "DFLT ", "gujr": "DFLT ", "gur2": "DFLT ", "guru": "DFLT ", "hebr": "DFLT IWR  JII  ", "latn": "DFLT ", "mlm2": "DFLT ", "mly2": "DFLT ", "mlym": "DFLT ", "phnx": "DFLT ", "syrc": "DFLT ", "tml2": "DFLT ", "ugar": "DFLT ", "xpeo": "DFLT "}, "GPOSFeatures": {"DFLT": {"DFLT": {"mark": [18]}}, "armn": {"DFLT": {"mark": [18]}}, "beng": {"DFLT": {"mark": [18], "blwm": [21]}}, "bng2": {"DFLT": {"mark": [18], "blwm": [21]}}, "cyrl": {"DFLT": {"mark": [13, 18], "mkmk": [14], "kern": [19]}}, "dev2": {"DFLT": {"abvm": [0], "blwm": [1], "mkmk": [2], "mark": [18]}}, "deva": {"DFLT": {"abvm": [0], "blwm": [1], "mkmk": [2], "mark": [18]}}, "geor": {"DFLT": {"mark": [18]}}, "gjr2": {"DFLT": {"mark": [18]}}, "grek": {"DFLT": {"mark": [18]}}, "gujr": {"DFLT": {"mark": [18]}}, "gur2": {"DFLT": {"mark": [18]}}, "guru": {"DFLT": {"abvm": [5], "blwm": [6, 8], "mkmk": [7]}}, "hebr": {"DFLT": {"mark": [11, 12, 18]}, "IWR ": {"mark": [11, 12]}, "JII ": {"mark": [10, 12]}}, "latn": {"DFLT": {"mark": [15, 16, 17, 18]}}, "mly2": {"DFLT": {"abvm": [3], "blwm": [4]}}, "mlym": {"DFLT": {"abvm": [3], "blwm": [4]}}, "phnx": {"DFLT": {"mark": [18]}}, "syrc": {"DFLT": {"mark": [9, 18]}}, "tml2": {"DFLT": {"mark": [18]}}, "ugar": {"DFLT": {"mark": [18]}}, "xpeo": {"DFLT": {"mark": [18]}}}, "GPOSLookups": [{"Type": 4, "Flag": 0, "SubtableCount": 3, "Subtables": [1538054, 1538240, 1538576], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1539992], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [1541572], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1541662], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1542000], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1542340, 1543030], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1543408], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [1544406], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1544494], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1545234], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [1545998], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 1, "SubtableCount": 1, "Subtables": [1546154], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 257, "SubtableCount": 1, "Subtables": [1546806], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1548210], "MarkFilteringSet": ""}, {"Type": 6, "Flag": 0, "SubtableCount": 1, "Subtables": [1549128], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1549194, 1551910], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1553016], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1555402], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 1, "Subtables": [1555776], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 0, "SubtableCount": 2, "Subtables": [1556794, 1556870], "MarkFilteringSet": ""}, {"Type": 2, "Flag": 0, "SubtableCount": 2, "Subtables": [1557160, 1559474], "MarkFilteringSet": ""}, {"Type": 4, "Flag": 0, "SubtableCount": 2, "Subtables": [1561084, 1561476], "MarkFilteringSet": ""}], "kerninfo": {"1027": {"1028": -10, "1040": -20, "1047": -10, "1054": -10, "1057": -10, "1060": -10, "1069": -10, "1071": -10, "1072": -20, "1074": -20, "1075": -20, "1076": -20, "1077": -20, "1078": -20, "1079": -20, "1080": -20, "1081": -20, "1082": -20, "1083": -20, "1084": -20, "1085": -20, "1086": -20, "1087": -20, "1088": -20, "1089": -20, "1090": -20, "1091": -20, "1092": -20, "1093": -20, "1094": -20, "1095": -20, "1096": -20, "1097": -20, "1098": -20, "1099": -20, "1100": -20, "1101": -20, "1102": -20, "1103": -20, "1104": -20, "1105": -20, "1106": -20, "1107": -20, "1108": -20, "1109": -20, "1110": -20, "1111": -20, "1112": -20, "1113": -20, "1114": -20, "1115": -20, "1116": -20, "1117": -20, "1118": -20, "1119": -20}, "1028": {"1058": -15, "1071": -10}, "1036": {"1028": -40, "1040": -10, "1047": -40, "1054": -40, "1057": -40, "1058": -30, "1060": -40, "1069": -40, "1071": -10, "1072": -20, "1074": -20, "1075": -20, "1076": -20, "1077": -20, "1078": -20, "1079": -20, "1080": -20, "1081": -20, "1082": -20, "1083": -20, "1084": -20, "1085": -20, "1086": -20, "1087": -20, "1088": -20, "1089": -20, "1090": -20, "1091": -20, "1092": -20, "1093": -20, "1094": -20, "1095": -20, "1096": -20, "1097": -20, "1098": -20, "1099": -20, "1100": -20, "1101": -20, "1102": -20, "1103": -20, "1104": -20, "1105": -20, "1106": -20, "1107": -20, "1108": -20, "1109": -20, "1110": -20, "1111": -20, "1112": -20, "1113": -20, "1114": -20, "1115": -20, "1116": -20, "1117": -20, "1118": -20, "1119": -20}, "1038": {"1028": -30, "1040": -50, "1047": -30, "1054": -30, "1057": -30, "1060": -30, "1069": -30, "1071": -20, "1072": -40, "1074": -40, "1075": -40, "1076": -40, "1077": -40, "1078": -40, "1079": -40, "1080": -40, "1081": -40, "1082": -40, "1083": -40, "1084": -40, "1085": -40, "1086": -40, "1087": -40, "1088": -40, "1089": -40, "1090": -40, "1091": -40, "1092": -40, "1093": -40, "1094": -40, "1095": -40, "1096": -40, "1097": -40, "1098": -40, "1099": -40, "1100": -40, "1101": -40, "1102": -40, "1103": -40, "1104": -40, "1105": -40, "1106": -40, "1107": -40, "1108": -40, "1109": -40, "1110": -40, "1111": -40, "1112": -40, "1113": -40, "1114": -40, "1115": -40, "1116": -40, "1117": -40, "1118": -40, "1119": -40}, "1043": {"1028": -10, "1040": -20, "1047": -10, "1054": -10, "1057": -10, "1060": -10, "1069": -10, "1071": -10, "1072": -20, "1074": -20, "1075": -20, "1076": -20, "1077": -20, "1078": -20, "1079": -20, "1080": -20, "1081": -20, "1082": -20, "1083": -20, "1084": -20, "1085": -20, "1086": -20, "1087": -20, "1088": -20, "1089": -20, "1090": -20, "1091": -20, "1092": -20, "1093": -20, "1094": -20, "1095": -20, "1096": -20, "1097": -20, "1098": -20, "1099": -20, "1100": -20, "1101": -20, "1102": -20, "1103": -20, "1104": -20, "1105": -20, "1106": -20, "1107": -20, "1108": -20, "1109": -20, "1110": -20, "1111": -20, "1112": -20, "1113": -20, "1114": -20, "1115": -20, "1116": -20, "1117": -20, "1118": -20, "1119": -20}, "1047": {"1058": -15, "1071": -10}, "1050": {"1028": -40, "1040": -10, "1047": -40, "1054": -40, "1057": -40, "1058": -30, "1060": -40, "1069": -40, "1071": -10, "1072": -20, "1074": -20, "1075": -20, "1076": -20, "1077": -20, "1078": -20, "1079": -20, "1080": -20, "1081": -20, "1082": -20, "1083": -20, "1084": -20, "1085": -20, "1086": -20, "1087": -20, "1088": -20, "1089": -20, "1090": -20, "1091": -20, "1092": -20, "1093": -20, "1094": -20, "1095": -20, "1096": -20, "1097": -20, "1098": -20, "1099": -20, "1100": -20, "1101": -20, "1102": -20, "1103": -20, "1104": -20, "1105": -20, "1106": -20, "1107": -20, "1108": -20, "1109": -20, "1110": -20, "1111": -20, "1112": -20, "1113": -20, "1114": -20, "1115": -20, "1116": -20, "1117": -20, "1118": -20, "1119": -20}, "1054": {"1058": -15, "1071": -10}, "1056": {"1028": -5, "1040": -30, "1047": -5, "1054": -5, "1057": -5, "1060": -5, "1069": -5, "1071": -5, "1072": -10, "1074": -10, "1075": -10, "1076": -10, "1077": -10, "1078": -10, "1079": -10, "1080": -10, "1081": -10, "1082": -10, "1083": -10, "1084": -10, "1085": -10, "1086": -10, "1087": -10, "1088": -10, "1089": -10, "1090": -10, "1091": -10, "1092": -10, "1093": -10, "1094": -10, "1095": -10, "1096": -10, "1097": -10, "1098": -10, "1099": -10, "1100": -10, "1101": -10, "1102": -10, "1103": -10, "1104": -10, "1105": -10, "1106": -10, "1107": -10, "1108": -10, "1109": -10, "1110": -10, "1111": -10, "1112": -10, "1113": -10, "1114": -10, "1115": -10, "1116": -10, "1117": -10, "1118": -10, "1119": -10}, "1057": {"1058": -15, "1071": -10}, "1058": {"1028": -10, "1040": -20, "1047": -10, "1054": -10, "1057": -10, "1060": -10, "1069": -10, "1071": -10, "1072": -20, "1074": -20, "1075": -20, "1076": -20, "1077": -20, "1078": -20, "1079": -20, "1080": -20, "1081": -20, "1082": -20, "1083": -20, "1084": -20, "1085": -20, "1086": -20, "1087": -20, "1088": -20, "1089": -20, "1090": -20, "1091": -20, "1092": -20, "1093": -20, "1094": -20, "1095": -20, "1096": -20, "1097": -20, "1098": -20, "1099": -20, "1100": -20, "1101": -20, "1102": -20, "1103": -20, "1104": -20, "1105": -20, "1106": -20, "1107": -20, "1108": -20, "1109": -20, "1110": -20, "1111": -20, "1112": -20, "1113": -20, "1114": -20, "1115": -20, "1116": -20, "1117": -20, "1118": -20, "1119": -20}, "1059": {"1028": -30, "1040": -50, "1047": -30, "1054": -30, "1057": -30, "1060": -30, "1069": -30, "1071": -20, "1072": -40, "1074": -40, "1075": -40, "1076": -40, "1077": -40, "1078": -40, "1079": -40, "1080": -40, "1081": -40, "1082": -40, "1083": -40, "1084": -40, "1085": -40, "1086": -40, "1087": -40, "1088": -40, "1089": -40, "1090": -40, "1091": -40, "1092": -40, "1093": -40, "1094": -40, "1095": -40, "1096": -40, "1097": -40, "1098": -40, "1099": -40, "1100": -40, "1101": -40, "1102": -40, "1103": -40, "1104": -40, "1105": -40, "1106": -40, "1107": -40, "1108": -40, "1109": -40, "1110": -40, "1111": -40, "1112": -40, "1113": -40, "1114": -40, "1115": -40, "1116": -40, "1117": -40, "1118": -40, "1119": -40}, "1060": {"1058": -15, "1071": -10}, "1066": {"1058": -15, "1071": -10}, "1068": {"1058": -15, "1071": -10}, "1069": {"1058": -15, "1071": -10}, "1070": {"1058": -15, "1071": -10}, "1075": {"1076": -20}, "1090": {"1076": -20}, "1091": {"1076": -10}, "1118": {"1076": -10}}}