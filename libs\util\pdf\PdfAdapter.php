<?php
/**
 * PDF库适配器
 * 提供从旧FPDF系统到新mPDF系统的平滑过渡
 */

class PdfAdapter
{
    /**
     * 字符串编码转换工具
     * 将GBK编码的字符串转换为UTF-8
     */
    public static function convertEncoding($str, $fromEncoding = 'GBK', $toEncoding = 'UTF-8')
    {
        if (empty($str)) {
            return $str;
        }
        
        // 检查是否已经是目标编码
        if (mb_check_encoding($str, $toEncoding)) {
            return $str;
        }
        
        // 尝试检测编码
        $detectedEncoding = mb_detect_encoding($str, ['UTF-8', 'GBK', 'GB2312'], true);
        if ($detectedEncoding && $detectedEncoding !== $toEncoding) {
            return mb_convert_encoding($str, $toEncoding, $detectedEncoding);
        }
        
        // 如果检测失败，使用指定的源编码
        return mb_convert_encoding($str, $toEncoding, $fromEncoding);
    }
    
    /**
     * 改进的字符串截取函数
     * 替换原有的pdfStrCut函数，支持UTF-8编码
     */
    public static function pdfStrCut($str = '', $rowLen = 0, $encoding = 'UTF-8')
    {
        if (empty($str) || $rowLen <= 0) {
            return $str;
        }
        
        // 确保字符串是UTF-8编码
        $str = self::convertEncoding($str, 'GBK', 'UTF-8');
        
        $linelen = $rowLen;
        $strLen = mb_strlen($str, $encoding);
        $num = ceil($strLen / $linelen);
        $add = array();
        
        for ($j = 0; $j < $num; $j++) {
            $start = $j * $linelen;
            $add[$j] = mb_substr($str, $start, $linelen, $encoding);
        }
        
        return implode("\n", $add);
    }
    
    /**
     * 批量转换数组中的编码
     */
    public static function convertArrayEncoding($array, $fromEncoding = 'GBK', $toEncoding = 'UTF-8')
    {
        if (!is_array($array)) {
            return self::convertEncoding($array, $fromEncoding, $toEncoding);
        }
        
        $result = [];
        foreach ($array as $key => $value) {
            $newKey = self::convertEncoding($key, $fromEncoding, $toEncoding);
            if (is_array($value)) {
                $result[$newKey] = self::convertArrayEncoding($value, $fromEncoding, $toEncoding);
            } else {
                $result[$newKey] = self::convertEncoding($value, $fromEncoding, $toEncoding);
            }
        }
        
        return $result;
    }
    
    /**
     * 字体映射
     * 将旧的字体名称映射到新的字体名称
     */
    public static function mapFontName($oldFontName)
    {
        $fontMap = [
            'GB' => 'simsun',
            'simsun' => 'simsun',
            'simhei' => 'simhei',
            'kaiti' => 'kaiti',
            'FZSTK' => 'kaiti',
            'SIMLI' => 'kaiti',
            '宋体' => 'simsun',
            '黑体' => 'simhei',
            '楷体' => 'kaiti',
            '华文楷体' => 'kaiti',
            '方正舒体' => 'kaiti',
            '隶书' => 'kaiti',
        ];
        
        return isset($fontMap[$oldFontName]) ? $fontMap[$oldFontName] : 'simsun';
    }
    
    /**
     * 创建兼容的PDF实例
     * 根据参数创建适当的PDF生成器实例
     */
    public static function createPdfInstance($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        // 检查是否存在mPDF
        if (class_exists('UniversalPdfGenerator')) {
            return new UniversalPdfGenerator($orientation, $unit, $size);
        }
        
        // 如果没有新的PDF生成器，回退到旧的实现
        if (class_exists('PDF_Chinese')) {
            return new PDF_Chinese($orientation, $unit, $size);
        }
        
        throw new Exception('No PDF generator available');
    }
    
    /**
     * 处理表格数据编码转换
     */
    public static function convertTableData($data)
    {
        if (!is_array($data)) {
            return [self::convertEncoding($data)];
        }
        
        $result = [];
        foreach ($data as $item) {
            if (is_array($item)) {
                $result[] = self::convertArrayEncoding($item);
            } else {
                $result[] = self::convertEncoding($item);
            }
        }
        
        return $result;
    }
    
    /**
     * 安全的文件路径处理
     */
    public static function sanitizeFilePath($path)
    {
        // 转换编码
        $path = self::convertEncoding($path);
        
        // 移除危险字符
        $path = preg_replace('/[^\w\-_\.\(\)\/\\\\]/', '', $path);
        
        return $path;
    }
    
    /**
     * 格式化金额显示
     */
    public static function formatMoney($amount, $currency = '￥')
    {
        if (!is_numeric($amount)) {
            return $amount;
        }
        
        return $currency . number_format($amount, 2);
    }
    
    /**
     * 日期格式转换
     */
    public static function formatDate($date, $format = 'Y年m月d日')
    {
        if (empty($date)) {
            return '';
        }
        
        $timestamp = is_numeric($date) ? $date : strtotime($date);
        if ($timestamp === false) {
            return $date;
        }
        
        return date($format, $timestamp);
    }
    
    /**
     * 处理二维码文本编码
     */
    public static function prepareQrCodeText($text)
    {
        // 确保二维码文本是UTF-8编码
        return self::convertEncoding($text);
    }
    
    /**
     * 验证PDF生成环境
     */
    public static function validateEnvironment()
    {
        $errors = [];
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.4.0', '<')) {
            $errors[] = 'PHP版本过低，建议使用7.4或更高版本';
        }
        
        // 检查必要的扩展
        $requiredExtensions = ['mbstring', 'gd', 'iconv'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                $errors[] = "缺少必要的PHP扩展: {$ext}";
            }
        }
        
        // 检查字体目录
        $fontDir = LIB_DIR . '/util/pdf/fonts';
        if (!is_dir($fontDir)) {
            $errors[] = "字体目录不存在: {$fontDir}";
        }
        
        return $errors;
    }
    
    /**
     * 获取系统信息
     */
    public static function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'mbstring_enabled' => extension_loaded('mbstring'),
            'gd_enabled' => extension_loaded('gd'),
            'iconv_enabled' => extension_loaded('iconv'),
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];
    }
}

/**
 * 全局辅助函数
 */

/**
 * 快速编码转换
 */
function pdf_convert_encoding($str, $from = 'GBK', $to = 'UTF-8')
{
    return PdfAdapter::convertEncoding($str, $from, $to);
}

/**
 * 快速字符串截取
 */
function pdf_str_cut($str, $length, $encoding = 'UTF-8')
{
    return PdfAdapter::pdfStrCut($str, $length, $encoding);
}

/**
 * 创建PDF实例
 */
function create_pdf($orientation = 'P', $unit = 'mm', $size = 'A4')
{
    return PdfAdapter::createPdfInstance($orientation, $unit, $size);
}
