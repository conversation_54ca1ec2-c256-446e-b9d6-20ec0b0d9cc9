# PDF乱码问题修复方案

## 问题描述

浏览器显示"未能加载 PDF 文档"，疑似内容乱码导致PDF文件损坏。

## 可能的原因

1. **编码问题**：文本编码不正确导致PDF内容损坏
2. **HTTP头问题**：缺少正确的Content-Type头
3. **输出缓冲问题**：PHP输出缓冲导致PDF内容被污染
4. **字体问题**：字体加载失败导致PDF生成错误
5. **mPDF配置问题**：mPDF配置不当导致输出异常

## 已实施的修复

### ✅ 1. 编码转换优化

```php
protected function safeEncodeText($text)
{
    // 多重编码检测和转换
    // 自动清理无效字符
    // 确保所有文本都是有效的UTF-8
}
```

### ✅ 2. 输出方法改进

```php
public function Output($name = '', $dest = 'I')
{
    // 设置正确的HTTP头
    if ($dest === 'I') {
        header('Content-Type: application/pdf; charset=UTF-8');
        header('Content-Disposition: inline; filename="' . $name . '"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
    }
    
    return $this->mpdf->Output($name, $dest);
}
```

### ✅ 3. 文本写入方法优化

```php
public function Write($h, $txt, $link = '')
{
    // 使用HTML方式写入，确保编码正确
    $html = '<span style="' . $style . '">' . htmlspecialchars($txt, ENT_QUOTES, 'UTF-8') . '</span>';
    $this->mpdf->WriteHTML($html);
}
```

### ✅ 4. 属性访问权限修复

- 将关键属性从private改为protected
- 确保子类可以正确访问父类属性

## 测试验证

### 运行测试脚本

1. **基础测试**：
   ```bash
   php simple_pdf_test.php
   ```

2. **完整测试**：
   ```bash
   php test_pdf_output.php
   ```

### 预期结果

```
开始简单PDF测试...
PDF实例创建成功！
内容添加成功！
✓ PDF格式正确
PDF大小: XXXX 字节
✓ PDF文件保存成功: simple_test.pdf
简单PDF测试完成！
```

## 故障排除

### 如果PDF仍然损坏

1. **检查输出缓冲**：
   ```php
   // 在PDF输出前清理缓冲区
   if (ob_get_level()) {
       ob_end_clean();
   }
   ```

2. **检查错误输出**：
   ```php
   // 临时禁用错误输出
   error_reporting(0);
   ini_set('display_errors', 0);
   ```

3. **检查HTTP头**：
   ```bash
   curl -I http://your-domain/pdf.php?action=index&id=123
   ```

4. **检查PDF内容**：
   ```php
   $pdfString = $pdf->Output('test.pdf', 'S');
   if (substr($pdfString, 0, 4) !== '%PDF') {
       echo "PDF格式错误！";
       echo "前100字节: " . bin2hex(substr($pdfString, 0, 100));
   }
   ```

### 常见问题解决

1. **BOM问题**：
   - 确保PHP文件没有BOM头
   - 检查include的文件是否有BOM

2. **空白输出**：
   - 检查是否有echo/print语句
   - 检查是否有空白字符输出

3. **字符集问题**：
   - 确保数据库连接使用UTF-8
   - 确保所有文本数据是UTF-8编码

## 临时解决方案

如果问题仍然存在，可以使用以下临时方案：

### 方案1：文件输出方式

```php
// 先输出到文件，再读取
$pdf->Output('/tmp/temp.pdf', 'F');
$pdfContent = file_get_contents('/tmp/temp.pdf');

header('Content-Type: application/pdf');
header('Content-Length: ' . strlen($pdfContent));
echo $pdfContent;
unlink('/tmp/temp.pdf');
```

### 方案2：Base64编码

```php
// 使用Base64编码传输
$pdfString = $pdf->Output('test.pdf', 'S');
$base64 = base64_encode($pdfString);

header('Content-Type: text/html');
echo '<iframe src="data:application/pdf;base64,' . $base64 . '" width="100%" height="600px"></iframe>';
```

### 方案3：分步调试

```php
try {
    $pdf = new PDF_Chinese();
    echo "1. PDF实例创建成功\n";
    
    $pdf->AddPage();
    echo "2. 页面添加成功\n";
    
    $pdf->SetFont('DejaVuSans', '', 12);
    echo "3. 字体设置成功\n";
    
    $pdf->Write(10, 'Test');
    echo "4. 文本写入成功\n";
    
    $pdfString = $pdf->Output('test.pdf', 'S');
    echo "5. PDF生成成功，大小: " . strlen($pdfString) . "\n";
    
    if (substr($pdfString, 0, 4) === '%PDF') {
        echo "6. PDF格式验证成功\n";
    } else {
        echo "6. PDF格式验证失败\n";
    }
    
} catch (Exception $e) {
    echo "错误在步骤: " . $e->getMessage() . "\n";
}
```

## 最佳实践

1. **输出前清理**：
   ```php
   ob_clean();
   header('Content-Type: application/pdf');
   ```

2. **错误处理**：
   ```php
   try {
       $pdf->Output('test.pdf', 'I');
   } catch (Exception $e) {
       header('Content-Type: text/html');
       echo "PDF生成失败: " . $e->getMessage();
   }
   ```

3. **编码统一**：
   - 确保所有文本数据统一使用UTF-8
   - 在数据入口处进行编码转换

4. **测试验证**：
   - 定期运行测试脚本
   - 检查生成的PDF文件完整性

修复完成后，PDF应该能正常在浏览器中显示！
