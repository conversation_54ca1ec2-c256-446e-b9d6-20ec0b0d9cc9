<?php
/**
 * 测试长文本不换行
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试长文本不换行...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 设置字体
    $pdf->SetFont('simsun', '', 12);
    
    // 模拟长公司名称
    $salecomp = ['ComName' => '销售方公司名称很长的有限责任公司'];
    $buycomp = ['ComName' => '采购方公司名称也很长的股份有限公司集团总部'];
    
    echo "测试1: 模拟实际场景\n";
    $pdf->SetX(10);
    $pdf->Write(10, '甲方：');
    $pdf->Write(10, $salecomp['ComName']);
    $pdf->SetX(110); 
    $pdf->Write(10, '乙方：');
    $pdf->Write(10, $buycomp['ComName']);
    $pdf->Ln();
    
    echo "测试2: 更长的文本\n";
    $pdf->SetX(10);
    $pdf->Write(10, '左侧：');
    $pdf->Write(10, '这是一个非常非常长的文本内容，用来测试是否会换行');
    $pdf->SetX(110);
    $pdf->Write(10, '右侧：');
    $pdf->Write(10, '这也是一个很长的文本，测试右侧是否换行');
    $pdf->Ln();
    
    echo "测试3: 检查页面宽度限制\n";
    $pdf->SetX(10);
    $pdf->Write(10, '短文本');
    $pdf->SetX(110);
    $pdf->Write(10, '右侧短文本');
    $pdf->Ln();
    
    echo "测试4: 极长文本测试\n";
    $pdf->SetX(10);
    $pdf->Write(10, '甲方：');
    $pdf->Write(10, '北京市海淀区中关村软件园某某科技发展有限责任公司');
    $pdf->SetX(110);
    $pdf->Write(10, '乙方：');
    $pdf->Write(10, '上海市浦东新区陆家嘴金融贸易区某某集团股份有限公司');
    $pdf->Ln();
    
    // 输出PDF
    $pdf->Output('test_long_text_nowrap.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: test_long_text_nowrap.pdf\n";
    echo "请检查PDF文件，确认：\n";
    echo "1. 乙方内容不会换行到下一行\n";
    echo "2. 长文本可能会超出页面边界，但不换行\n";
    echo "3. 甲方和乙方分别在10mm和110mm位置开始\n";
    
    echo "\n如果乙方文本太长超出页面，可能需要：\n";
    echo "1. 缩短公司名称\n";
    echo "2. 调整乙方起始位置（比如改为100mm）\n";
    echo "3. 使用更小的字体\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
