<?php
/**
 * 测试SetX修复 - 模拟实际使用场景
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试SetX修复...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 设置字体
    $pdf->SetFont('simsun', '', 12);
    
    // 模拟您的实际代码
    echo "测试实际代码场景...\n";
    
    // 模拟公司名称
    $salecomp = ['ComName' => '销售公司名称有限公司'];
    $buycomp = ['ComName' => '采购公司名称有限责任公司'];
    
    // 完全按照您的代码逻辑
    $pdf->SetX(10);
    $pdf->Write(10, '甲方：');
    $pdf->Write(10, $salecomp['ComName']);
    $pdf->SetX(110); 
    $pdf->Write(10, '乙方：');
    $pdf->Write(10, $buycomp['ComName']);
    $pdf->Ln();
    
    echo "第一行测试完成\n";
    
    // 再测试一行，确保效果一致
    $pdf->SetX(10);
    $pdf->Write(10, '测试1：');
    $pdf->Write(10, '左侧内容');
    $pdf->SetX(110);
    $pdf->Write(10, '测试2：');
    $pdf->Write(10, '右侧内容');
    $pdf->Ln();
    
    echo "第二行测试完成\n";
    
    // 测试更复杂的情况
    $pdf->SetX(10);
    $pdf->Write(10, 'A');
    $pdf->SetX(50);
    $pdf->Write(10, 'B');
    $pdf->SetX(90);
    $pdf->Write(10, 'C');
    $pdf->SetX(130);
    $pdf->Write(10, 'D');
    $pdf->Ln();
    
    echo "多列测试完成\n";
    
    // 输出PDF
    $pdf->Output('test_setx_fix.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: test_setx_fix.pdf\n";
    echo "请检查PDF文件，确认：\n";
    echo "1. '甲方：' 出现在X=10mm位置\n";
    echo "2. '乙方：' 出现在X=110mm位置\n";
    echo "3. 两个部分不会重叠\n";
    echo "4. 位置设置正确生效\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
