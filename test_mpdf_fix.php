<?php
/**
 * 测试mPDF临时目录修复
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试mPDF临时目录修复...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    echo "PDF实例创建成功！\n";
    
    $pdf->AddPage();
    echo "页面添加成功！\n";
    
    // 设置字体并写入文本
    $pdf->SetFont('DejaVuSans', '', 12);
    $pdf->Write(10, 'Test PDF Generation - 测试PDF生成');
    $pdf->Ln();
    $pdf->Write(10, 'Temporary directory issue fixed!');
    
    // 输出PDF
    $pdf->Output('test_temp_fix.pdf', 'F');
    
    echo "PDF生成成功！临时目录问题已修复！\n";
    echo "文件保存为: test_temp_fix.pdf\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
