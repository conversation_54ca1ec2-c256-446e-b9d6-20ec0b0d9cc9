<?php
/**
 * 创建mPDF临时目录脚本
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

echo "开始创建mPDF临时目录...\n";

// 需要创建的目录列表
$directories = [
    LIB_DIR . '/util/pdf/tmp',
    LIB_DIR . '/util/pdf/fonts',
    LIB_DIR . '/vendor/mpdf/tmp',
    '/tmp/mpdf',
];

foreach ($directories as $dir) {
    echo "创建目录: {$dir}\n";
    
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "  ✓ 目录创建成功\n";
        } else {
            echo "  ✗ 目录创建失败\n";
            continue;
        }
    } else {
        echo "  ✓ 目录已存在\n";
    }
    
    // 检查权限
    if (is_writable($dir)) {
        echo "  ✓ 目录可写\n";
    } else {
        echo "  ✗ 目录不可写，尝试修改权限...\n";
        if (chmod($dir, 0755)) {
            echo "  ✓ 权限修改成功\n";
        } else {
            echo "  ✗ 权限修改失败\n";
        }
    }
    
    echo "\n";
}

// 测试系统临时目录
echo "检查系统临时目录...\n";
$systemTempDir = sys_get_temp_dir();
echo "系统临时目录: {$systemTempDir}\n";

if (is_writable($systemTempDir)) {
    echo "  ✓ 系统临时目录可写\n";
    
    // 在系统临时目录创建mpdf子目录
    $mpdfTempDir = $systemTempDir . '/mpdf';
    if (!is_dir($mpdfTempDir)) {
        if (mkdir($mpdfTempDir, 0755, true)) {
            echo "  ✓ 在系统临时目录创建mpdf子目录成功\n";
        } else {
            echo "  ✗ 在系统临时目录创建mpdf子目录失败\n";
        }
    } else {
        echo "  ✓ 系统临时目录中的mpdf子目录已存在\n";
    }
} else {
    echo "  ✗ 系统临时目录不可写\n";
}

echo "\n目录创建完成！\n";

// 创建一个测试文件来验证权限
$testFile = LIB_DIR . '/util/pdf/tmp/test_write.txt';
if (file_put_contents($testFile, 'test') !== false) {
    echo "✓ 临时目录写入测试成功\n";
    unlink($testFile);
} else {
    echo "✗ 临时目录写入测试失败\n";
    echo "请手动执行以下命令修复权限：\n";
    echo "chmod -R 755 " . LIB_DIR . "/util/pdf/tmp\n";
    echo "chown -R www-data:www-data " . LIB_DIR . "/util/pdf/tmp\n";
}

echo "\n请运行以下命令测试PDF生成：\n";
echo "php test_mpdf_fix.php\n";

// 显示当前用户和权限信息
echo "\n=== 调试信息 ===\n";
echo "当前PHP用户: " . get_current_user() . "\n";
echo "当前进程用户ID: " . getmyuid() . "\n";
echo "当前进程组ID: " . getmygid() . "\n";
echo "系统临时目录: " . sys_get_temp_dir() . "\n";
echo "项目临时目录: " . LIB_DIR . '/util/pdf/tmp' . "\n";
?>
