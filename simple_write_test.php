<?php
/**
 * 简单的Write方法测试
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始简单Write测试...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 设置字体
    $pdf->SetFont('simsun', '', 12);
    
    // 测试连续写入
    echo "测试连续写入...\n";
    $pdf->Write(10, '第一段');
    $pdf->Write(10, '第二段');
    $pdf->Write(10, '第三段');
    
    // 输出PDF
    $pdf->Output('simple_write_test.pdf', 'F');
    
    echo "PDF生成成功！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
