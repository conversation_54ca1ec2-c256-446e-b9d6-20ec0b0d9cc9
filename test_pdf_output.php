<?php
/**
 * 测试PDF输出
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试PDF输出...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simhei', '黑体');
    $pdf->AddGBFont('FZSTK', '方正舒体');
    $pdf->AddGBFont('SIMLI', '隶书');
    $pdf->AddGBFont('simsun', '宋体');
    $pdf->AddGBFont('kaiti', '华文楷体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    echo "PDF实例创建成功！\n";
    
    // 设置字体并写入文本
    $pdf->SetFont('simsun', '', 14);
    $pdf->Write(10, '测试PDF输出功能');
    $pdf->Ln();
    
    $pdf->SetFont('simhei', 'B', 12);
    $pdf->Write(10, '这是黑体粗体文字');
    $pdf->Ln();
    
    $pdf->SetFont('kaiti', '', 12);
    $pdf->Write(10, '这是楷体文字');
    $pdf->Ln();
    
    // 测试表格
    $pdf->SetWidths([60, 60, 60]);
    $pdf->SetAligns(['C', 'C', 'C']);
    $pdf->Row(['中文列1', '中文列2', '中文列3']);
    $pdf->Row(['数据1', '数据2', '数据3']);
    $pdf->Row(['测试中文', '编码转换', 'UTF-8']);
    
    echo "内容添加成功！\n";
    
    // 测试不同的输出方式
    echo "测试文件输出...\n";
    $pdf->Output('test_output.pdf', 'F');
    echo "文件输出成功！\n";
    
    echo "测试字符串输出...\n";
    $pdfString = $pdf->Output('test_string.pdf', 'S');
    echo "字符串输出成功！长度: " . strlen($pdfString) . " 字节\n";
    
    // 检查PDF内容是否有效
    if (substr($pdfString, 0, 4) === '%PDF') {
        echo "✓ PDF格式正确\n";
    } else {
        echo "✗ PDF格式错误\n";
        echo "前20字节: " . bin2hex(substr($pdfString, 0, 20)) . "\n";
    }
    
    echo "\nPDF输出测试完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
