#!/bin/bash

# mPDF临时目录权限修复脚本

echo "开始修复mPDF临时目录权限问题..."

# 项目根目录
PROJECT_ROOT="/usr/local/www/diis"

# 创建必要的目录
echo "创建临时目录..."
mkdir -p "${PROJECT_ROOT}/libs/util/pdf/tmp"
mkdir -p "${PROJECT_ROOT}/libs/util/pdf/fonts"
mkdir -p "${PROJECT_ROOT}/libs/vendor/mpdf/tmp"
mkdir -p "/tmp/mpdf"

# 设置权限
echo "设置目录权限..."
chmod -R 755 "${PROJECT_ROOT}/libs/util/pdf/tmp"
chmod -R 755 "${PROJECT_ROOT}/libs/util/pdf/fonts"
chmod -R 755 "${PROJECT_ROOT}/libs/vendor/mpdf/tmp"
chmod -R 755 "/tmp/mpdf"

# 设置所有者（假设Web服务器用户是www-data）
echo "设置目录所有者..."
chown -R www-data:www-data "${PROJECT_ROOT}/libs/util/pdf/tmp"
chown -R www-data:www-data "${PROJECT_ROOT}/libs/util/pdf/fonts"
chown -R www-data:www-data "${PROJECT_ROOT}/libs/vendor/mpdf/tmp"
chown -R www-data:www-data "/tmp/mpdf"

# 检查系统临时目录权限
echo "检查系统临时目录权限..."
ls -la /tmp | grep mpdf

# 如果是CentOS/RHEL系统，可能需要设置SELinux上下文
if command -v getenforce &> /dev/null; then
    if [ "$(getenforce)" != "Disabled" ]; then
        echo "设置SELinux上下文..."
        setsebool -P httpd_can_network_connect 1
        chcon -R -t httpd_exec_t "${PROJECT_ROOT}/libs/util/pdf/tmp"
        chcon -R -t httpd_exec_t "/tmp/mpdf"
    fi
fi

echo "权限修复完成！"
echo ""
echo "请运行以下命令测试："
echo "cd ${PROJECT_ROOT}"
echo "php test_mpdf_fix.php"
