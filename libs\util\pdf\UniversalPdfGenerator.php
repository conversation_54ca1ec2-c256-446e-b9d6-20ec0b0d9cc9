<?php
/**
 * 通用PDF生成器类
 * 用于替换原有的FPDF库，支持PHP 8.1和UTF-8编码
 * 兼容原有的PDF_Chinese类接口
 */

require_once LIB_DIR . '/vendor/autoload.php'; // 使用LIB_DIR常量

use Mpdf\Mpdf;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;

class UniversalPdfGenerator
{
    private $mpdf;
    private $currentX = 0;
    private $currentY = 0;
    private $pageWidth = 210; // A4宽度(mm)
    private $pageHeight = 297; // A4高度(mm)
    private $leftMargin = 10;
    private $topMargin = 10;
    private $rightMargin = 10;
    private $bottomMargin = 10;
    private $currentFont = 'simsun';
    private $currentFontSize = 12;
    private $currentFontStyle = '';
    private $lineHeight = 5;
    
    // 表格相关属性
    private $tableWidths = [];
    private $tableAligns = [];
    private $tableHeight = 6;
    
    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        // 配置中文字体
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        
        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];
        
        // 添加中文字体配置
        $fontData = $fontData + [
            'simsun' => [
                'R' => 'simsun.ttf',
                'B' => 'simsun.ttf',
            ],
            'simhei' => [
                'R' => 'simhei.ttf',
                'B' => 'simhei.ttf',
            ],
            'kaiti' => [
                'R' => 'kaiti.ttf',
                'B' => 'kaiti.ttf',
            ],
        ];
        
        $config = [
            'fontDir' => array_merge($fontDirs, [LIB_DIR . '/util/pdf/fonts']),
            'fontdata' => $fontData,
            'default_font' => 'simsun',
            'orientation' => $orientation,
            'format' => $size,
            'margin_left' => $this->leftMargin,
            'margin_right' => $this->rightMargin,
            'margin_top' => $this->topMargin,
            'margin_bottom' => $this->bottomMargin,
        ];
        
        $this->mpdf = new Mpdf($config);
        $this->mpdf->SetAutoFont();
    }
    
    /**
     * 兼容原有的AddGBFont方法
     */
    public function AddGBFont($family = '', $name = '')
    {
        // mPDF自动处理中文字体，这里保持兼容性
        return true;
    }
    
    /**
     * 兼容原有的Open方法
     */
    public function Open()
    {
        // mPDF自动处理，保持兼容性
        return true;
    }
    
    /**
     * 添加页面
     */
    public function AddPage($orientation = '', $size = '')
    {
        if ($this->mpdf->page > 0) {
            $this->mpdf->AddPage($orientation, '', '', '', '', 
                $this->leftMargin, $this->rightMargin, 
                $this->topMargin, $this->bottomMargin);
        }
        $this->currentX = $this->leftMargin;
        $this->currentY = $this->topMargin;
    }
    
    /**
     * 设置字体
     */
    public function SetFont($family, $style = '', $size = 0)
    {
        $this->currentFont = $family;
        $this->currentFontStyle = $style;
        if ($size > 0) {
            $this->currentFontSize = $size;
        }
        
        // 映射字体名称
        $fontMap = [
            'GB' => 'simsun',
            'simsun' => 'simsun',
            'simhei' => 'simhei',
            'kaiti' => 'kaiti',
            'FZSTK' => 'kaiti',
            'SIMLI' => 'kaiti',
        ];
        
        $mappedFont = isset($fontMap[$family]) ? $fontMap[$family] : 'simsun';
        $this->mpdf->SetFont($mappedFont, $style, $this->currentFontSize);
    }
    
    /**
     * 设置位置
     */
    public function SetX($x)
    {
        $this->currentX = $x;
    }
    
    public function SetY($y)
    {
        $this->currentY = $y;
    }
    
    public function SetXY($x, $y)
    {
        $this->currentX = $x;
        $this->currentY = $y;
    }
    
    /**
     * 获取当前位置
     */
    public function GetX()
    {
        return $this->currentX;
    }
    
    public function GetY()
    {
        return $this->currentY;
    }
    
    /**
     * 写入文本
     */
    public function Write($h, $txt, $link = '')
    {
        $this->lineHeight = $h;
        
        // 转换编码为UTF-8
        if (!mb_check_encoding($txt, 'UTF-8')) {
            $txt = mb_convert_encoding($txt, 'UTF-8', 'GBK');
        }
        
        // 计算文本宽度
        $textWidth = $this->mpdf->GetStringWidth($txt);
        
        // 使用WriteText方法写入文本
        $this->mpdf->WriteText($this->currentX, $this->currentY, $txt);
        
        // 更新当前位置
        $this->currentX += $textWidth;
    }
    
    /**
     * 换行
     */
    public function Ln($h = null)
    {
        if ($h === null) {
            $h = $this->lineHeight;
        }
        $this->currentX = $this->leftMargin;
        $this->currentY += $h;
    }
    
    /**
     * 画线
     */
    public function Line($x1, $y1, $x2, $y2)
    {
        $this->mpdf->Line($x1, $y1, $x2, $y2);
    }
    
    /**
     * 插入图片
     */
    public function Image($file, $x = null, $y = null, $w = 0, $h = 0, $type = '', $link = '')
    {
        if ($x === null) $x = $this->currentX;
        if ($y === null) $y = $this->currentY;
        
        $this->mpdf->Image($file, $x, $y, $w, $h, $type, $link);
    }
    
    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->tableWidths = $widths;
    }
    
    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        if (is_string($aligns)) {
            // 如果是字符串，转换为数组
            $alignArray = [];
            for ($i = 0; $i < count($this->tableWidths); $i++) {
                $alignArray[] = $aligns;
            }
            $this->tableAligns = $alignArray;
        } else {
            $this->tableAligns = $aligns;
        }
    }
    
    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->tableHeight = $height;
    }
    
    /**
     * 绘制表格行
     */
    public function Row($data)
    {
        if (empty($this->tableWidths)) {
            return;
        }
        
        $html = '<table style="width: 100%; border-collapse: collapse;">';
        $html .= '<tr>';
        
        for ($i = 0; $i < count($data); $i++) {
            $width = isset($this->tableWidths[$i]) ? $this->tableWidths[$i] : 20;
            $align = isset($this->tableAligns[$i]) ? strtolower($this->tableAligns[$i]) : 'left';
            
            if ($align === 'c' || $align === 'center') {
                $align = 'center';
            } elseif ($align === 'r' || $align === 'right') {
                $align = 'right';
            } else {
                $align = 'left';
            }
            
            $cellData = isset($data[$i]) ? $data[$i] : '';
            
            // 转换编码
            if (!mb_check_encoding($cellData, 'UTF-8')) {
                $cellData = mb_convert_encoding($cellData, 'UTF-8', 'GBK');
            }
            
            $html .= '<td style="width: ' . $width . 'mm; text-align: ' . $align . '; border: 1px solid #000; padding: 2px; height: ' . $this->tableHeight . 'mm;">';
            $html .= htmlspecialchars($cellData, ENT_QUOTES, 'UTF-8');
            $html .= '</td>';
        }
        
        $html .= '</tr>';
        $html .= '</table>';
        
        $this->mpdf->WriteHTML($html);
        $this->currentY += $this->tableHeight;
    }
    
    /**
     * 输出PDF
     */
    public function Output($name = '', $dest = 'I')
    {
        return $this->mpdf->Output($name, $dest);
    }
    
    /**
     * 设置自动分页
     */
    public function SetAutoPageBreak($auto, $margin = 0)
    {
        // mPDF自动处理分页
        return true;
    }
}

/**
 * 增强的PDF_Chinese类
 * 继承UniversalPdfGenerator，添加原有系统需要的特定功能
 */
class PDF_Chinese extends UniversalPdfGenerator
{
    // 表格相关属性
    private $widths = [];
    private $height = 6;
    private $aligns = [];
    private $valigns = [];
    private $fills = [];
    private $innerBorderX = true;
    private $innerBorderY = true;
    private $doBorder = true;
    private $doCalculateHeight = true;
    private $zeilenhoehe = 4.5;

    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        parent::__construct($orientation, $unit, $size);
    }

    /**
     * 兼容原有的AddCIDFont方法
     */
    public function AddCIDFont($family, $style, $name, $cw, $CMap, $registry)
    {
        // mPDF自动处理CID字体，这里保持兼容性
        return true;
    }

    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->widths = $widths;
        parent::SetWidths($widths);
    }

    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        $this->aligns = is_array($aligns) ? $aligns : [$aligns];
        parent::SetAligns($aligns);
    }

    /**
     * 设置垂直对齐方式
     */
    public function SetValigns($valigns)
    {
        $this->valigns = is_array($valigns) ? $valigns : [$valigns];
    }

    /**
     * 设置填充色
     */
    public function SetFills($fills)
    {
        $this->fills = is_array($fills) ? $fills : [$fills];
    }

    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->zeilenhoehe = $height;
        $this->height = $height;
        parent::SetZeilenhoehe($height);
    }

    /**
     * 增强的表格行绘制
     */
    public function Row($data)
    {
        // 转换编码
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        $convertedData = [];
        foreach ($data as $item) {
            $convertedData[] = PdfAdapter::convertEncoding($item);
        }

        parent::Row($convertedData);
    }

    /**
     * 兼容原有的Cell方法
     */
    public function Cell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        // 转换编码
        $txt = PdfAdapter::convertEncoding($txt);

        // 使用HTML表格模拟Cell功能
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }

        $alignStyle = '';
        if ($align) {
            $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right'];
            $alignStyle = 'text-align: ' . (isset($alignMap[$align]) ? $alignMap[$align] : 'left') . ';';
        }

        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }

        $html = '<table style="width: ' . $w . 'mm; height: ' . $h . 'mm; border-collapse: collapse;">';
        $html .= '<tr><td style="' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px;">';
        $html .= htmlspecialchars($txt, ENT_QUOTES, 'UTF-8');
        $html .= '</td></tr></table>';

        $this->mpdf->WriteHTML($html);

        if ($ln == 1) {
            $this->Ln($h);
        } elseif ($ln == 2) {
            $this->SetXY($this->leftMargin, $this->GetY() + $h);
        }
    }

    /**
     * 多字节字符串写入
     */
    public function MBWrite($h, $txt, $link = '')
    {
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);
        return $this->Write($h, $txt, $link);
    }

    /**
     * 多字节字符串单元格
     */
    public function MBCell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);
        return $this->Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }

    /**
     * 多字节字符串多行输出
     */
    public function MBMultiCell($w, $h, $txt, $border = 0, $align = 'J', $fill = false)
    {
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);

        // 使用HTML实现多行文本
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }

        $alignStyle = '';
        $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right', 'J' => 'justify'];
        if (isset($alignMap[$align])) {
            $alignStyle = 'text-align: ' . $alignMap[$align] . ';';
        }

        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }

        $html = '<div style="width: ' . $w . 'mm; ' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px; line-height: ' . $h . 'mm;">';
        $html .= nl2br(htmlspecialchars($txt, ENT_QUOTES, 'UTF-8'));
        $html .= '</div>';

        $this->mpdf->WriteHTML($html);
    }

    /**
     * 获取字符串宽度
     */
    public function GetStringWidth($s)
    {
        require_once LIB_DIR . '/util/pdf/PdfAdapter.php';
        $s = PdfAdapter::convertEncoding($s);
        return $this->mpdf->GetStringWidth($s);
    }

    /**
     * 设置文本颜色
     */
    public function SetTextColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            // 灰度模式
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            // RGB模式
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetTextColor($color);
    }

    /**
     * 设置绘图颜色
     */
    public function SetDrawColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetDrawColor($color);
    }

    /**
     * 设置填充颜色
     */
    public function SetFillColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetFillColor($color);
    }

    /**
     * 画矩形
     */
    public function Rect($x, $y, $w, $h, $style = '')
    {
        $this->mpdf->Rect($x, $y, $w, $h, $style);
    }

    /**
     * 设置页边距
     */
    public function SetMargins($left, $top, $right = null)
    {
        if ($right === null) {
            $right = $left;
        }
        $this->leftMargin = $left;
        $this->topMargin = $top;
        $this->rightMargin = $right;
        $this->mpdf->SetMargins($left, $right, $top);
    }

    /**
     * 设置左边距
     */
    public function SetLeftMargin($margin)
    {
        $this->leftMargin = $margin;
        $this->mpdf->SetLeftMargin($margin);
    }

    /**
     * 设置上边距
     */
    public function SetTopMargin($margin)
    {
        $this->topMargin = $margin;
        $this->mpdf->SetTopMargin($margin);
    }

    /**
     * 设置右边距
     */
    public function SetRightMargin($margin)
    {
        $this->rightMargin = $margin;
        $this->mpdf->SetRightMargin($margin);
    }
}
