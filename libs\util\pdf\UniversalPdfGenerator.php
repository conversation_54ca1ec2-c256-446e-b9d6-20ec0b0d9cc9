<?php
/**
 * 通用PDF生成器类
 * 用于替换原有的FPDF库，支持PHP 8.1和UTF-8编码
 * 兼容原有的PDF_Chinese类接口
 */

require_once LIB_DIR . '/vendor/autoload.php'; // 使用LIB_DIR常量

use Mpdf\Mpdf;

class UniversalPdfGenerator
{
    protected $mpdf;
    protected $currentX = 0;
    protected $currentY = 0;
    protected $pageWidth = 210; // A4宽度(mm)
    protected $pageHeight = 297; // A4高度(mm)
    protected $leftMargin = 10;
    protected $topMargin = 10;
    protected $rightMargin = 10;
    protected $bottomMargin = 10;
    protected $currentFont = 'simsun';
    protected $currentFontSize = 12;
    protected $currentFontStyle = '';
    protected $lineHeight = 5;
    
    // 表格相关属性
    protected $tableWidths = [];
    protected $tableAligns = [];
    protected $tableHeight = 6;

    // HTML缓冲区，用于连续输出
    protected $htmlBuffer = '';

    // 图片垂直对齐偏移量（mm）
    protected $imageVerticalOffset = 1.5;
    
    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        // 设置环境变量，强制指定临时目录
        $tempDir = LIB_DIR . '/util/pdf/tmp';

        // 创建临时目录
        if (!is_dir($tempDir)) {
            @mkdir($tempDir, 0777, true);
        }

        // 如果创建失败或不可写，尝试其他位置
        if (!is_dir($tempDir) || !is_writable($tempDir)) {
            $tempDir = sys_get_temp_dir() . '/mpdf_' . uniqid();
            @mkdir($tempDir, 0777, true);
        }

        // 设置环境变量
        putenv("TMPDIR={$tempDir}");
        $_ENV['TMPDIR'] = $tempDir;

        try {
            // 配置mPDF，支持中文字体
            $config = [
                'mode' => 'utf-8',
                'format' => $size,
                'orientation' => $orientation,
                'margin_left' => $this->leftMargin,
                'margin_right' => $this->rightMargin,
                'margin_top' => $this->topMargin,
                'margin_bottom' => $this->bottomMargin,
                'tempDir' => $tempDir,
                'default_font' => 'dejavusans', // 使用DejaVu字体，支持更多字符
                'autoScriptToLang' => true,
                'autoLangToFont' => true,
                'useSubstitutions' => true,
            ];

            $this->mpdf = new Mpdf($config);

            // 设置中文字体支持
            $this->mpdf->autoScriptToLang = true;
            $this->mpdf->autoLangToFont = true;
            $this->mpdf->useSubstitutions = true;

        } catch (Exception $e) {
            // 最后的备用方案：使用内存模式
            try {
                $this->mpdf = new Mpdf([
                    'mode' => 'utf-8',
                    'format' => 'A4',
                    'tempDir' => $tempDir,
                    'default_font' => 'dejavusanscondensed',
                ]);
            } catch (Exception $e2) {
                // 如果还是失败，抛出详细错误信息
                throw new Exception("无法创建PDF实例: " . $e2->getMessage() . "\n临时目录: {$tempDir}\n权限: " . (is_writable($tempDir) ? '可写' : '不可写'));
            }
        }
    }
    
    /**
     * 兼容原有的AddGBFont方法
     */
    public function AddGBFont($family = '', $name = '')
    {
        // mPDF自动处理中文字体，这里保持兼容性
        return true;
    }
    
    /**
     * 兼容原有的Open方法
     */
    public function Open()
    {
        // mPDF自动处理，保持兼容性
        return true;
    }
    
    /**
     * 添加页面
     */
    public function AddPage($orientation = '', $size = '')
    {
        if ($this->mpdf->page > 0) {
            $this->mpdf->AddPage($orientation, '', '', '', '', 
                $this->leftMargin, $this->rightMargin, 
                $this->topMargin, $this->bottomMargin);
        }
        $this->currentX = $this->leftMargin;
        $this->currentY = $this->topMargin;
    }
    
    /**
     * 设置字体
     */
    public function SetFont($family, $style = '', $size = 0)
    {
        $this->currentFont = $family;
        $this->currentFontStyle = $style;
        if ($size > 0) {
            $this->currentFontSize = $size;
        }

        // 映射字体名称到支持中文的字体
        $fontMap = [
            'GB' => 'dejavusans',
            'simsun' => 'dejavusans',
            'simhei' => 'dejavusans',
            'kaiti' => 'dejavusans',
            'FZSTK' => 'dejavusans',
            'SIMLI' => 'dejavusans',
            '宋体' => 'dejavusans',
            '黑体' => 'dejavusans',
            '楷体' => 'dejavusans',
            '华文楷体' => 'dejavusans',
            '方正舒体' => 'dejavusans',
            '隶书' => 'dejavusans',
        ];

        $mappedFont = isset($fontMap[$family]) ? $fontMap[$family] : 'dejavusans';

        try {
            $this->mpdf->SetFont($mappedFont, $style, $this->currentFontSize);
        } catch (Exception $e) {
            // 如果字体设置失败，使用默认字体
            $this->mpdf->SetFont('dejavusanscondensed', $style, $this->currentFontSize);
        }
    }
    
    /**
     * 设置位置
     */
    public function SetX($x)
    {
        // 先刷新当前的HTML缓冲区，确保之前的内容已输出
        $this->flushHtmlBuffer();

        // 更新位置
        $this->currentX = $x;
        $this->mpdf->x = $x;

        // 使用绝对定位设置新的起始位置，并设置不换行
        $this->htmlBuffer .= '<span style="position: absolute; left: ' . $x . 'mm; top: ' . $this->mpdf->y . 'mm; white-space: nowrap;"></span>';
    }

    public function SetY($y)
    {
        // 先刷新当前的HTML缓冲区
        $this->flushHtmlBuffer();

        $this->currentY = $y;
        $this->mpdf->y = $y;
    }

    public function SetXY($x, $y)
    {
        // 先刷新当前的HTML缓冲区
        $this->flushHtmlBuffer();

        $this->currentX = $x;
        $this->currentY = $y;
        $this->mpdf->x = $x;
        $this->mpdf->y = $y;
    }
    
    /**
     * 获取当前位置
     */
    public function GetX()
    {
        return $this->mpdf->x;
    }

    public function GetY()
    {
        return $this->mpdf->y;
    }
    
    /**
     * 写入文本
     */
    public function Write($h, $txt, $link = '')
    {
        $this->lineHeight = $h;

        // 安全的编码转换
        $txt = $this->safeEncodeText($txt);

        // 如果是空白字符串或只包含空格，直接返回，不输出任何内容
        if (trim($txt) === '') {
            return;
        }

        // 添加到HTML缓冲区
        $fontFamily = 'dejavusans, sun-exta, dejavusanscondensed, sans-serif';
        $fontSize = $this->currentFontSize;
        $fontStyle = $this->currentFontStyle;

        $style = "font-family: {$fontFamily}; font-size: {$fontSize}pt;";
        if (strpos($fontStyle, 'B') !== false) {
            $style .= " font-weight: bold;";
        }
        if (strpos($fontStyle, 'I') !== false) {
            $style .= " font-style: italic;";
        }
        if (strpos($fontStyle, 'U') !== false) {
            $style .= " text-decoration: underline;";
        }

        // 添加到缓冲区而不是立即输出，确保不换行
        $this->htmlBuffer .= '<span style="' . $style . ' white-space: nowrap;">' . htmlspecialchars($txt, ENT_QUOTES, 'UTF-8') . '</span>';
    }

    /**
     * 安全的文本编码转换
     */
    protected function safeEncodeText($text)
    {
        if (empty($text)) {
            return '';
        }

        // 如果已经是有效的UTF-8，直接返回
        if (mb_check_encoding($text, 'UTF-8')) {
            return $text;
        }

        // 尝试从GBK转换
        $converted = @mb_convert_encoding($text, 'UTF-8', 'GBK');
        if (mb_check_encoding($converted, 'UTF-8')) {
            return $converted;
        }

        // 尝试从GB2312转换
        $converted = @mb_convert_encoding($text, 'UTF-8', 'GB2312');
        if (mb_check_encoding($converted, 'UTF-8')) {
            return $converted;
        }

        // 尝试自动检测编码
        $encoding = mb_detect_encoding($text, ['UTF-8', 'GBK', 'GB2312', 'ISO-8859-1'], true);
        if ($encoding) {
            $converted = @mb_convert_encoding($text, 'UTF-8', $encoding);
            if (mb_check_encoding($converted, 'UTF-8')) {
                return $converted;
            }
        }

        // 最后的清理：移除无效字符
        return mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    }
    
    /**
     * 换行
     */
    public function Ln($h = null)
    {
        if ($h === null) {
            $h = $this->lineHeight;
        }

        // 刷新HTML缓冲区并换行
        $this->flushHtmlBuffer();
        $this->mpdf->WriteHTML('<br />');

        // 更新内部位置变量
        $this->currentX = $this->leftMargin;
        $this->currentY += $h;
    }

    /**
     * 刷新HTML缓冲区
     */
    public function flushHtmlBuffer()
    {
        if (!empty($this->htmlBuffer)) {
            $this->mpdf->WriteHTML($this->htmlBuffer);
            $this->htmlBuffer = '';
        }
    }

    /**
     * 设置图片垂直对齐偏移量
     * @param float $offset 偏移量（mm），正值向下，负值向上
     */
    public function SetImageVerticalOffset($offset)
    {
        $this->imageVerticalOffset = $offset;
    }
    
    /**
     * 画线
     */
    public function Line($x1, $y1, $x2, $y2)
    {
        $this->mpdf->Line($x1, $y1, $x2, $y2);
    }
    
    /**
     * 插入图片
     */
    public function Image($file, $x = null, $y = null, $w = 0, $h = 0, $type = '', $link = '')
    {
        if ($x === null) $x = $this->currentX;
        if ($y === null) $y = $this->currentY;

        // 调整Y位置，使图片与文字基线对齐
        // 使用固定偏移量，可以根据需要调整
        $adjustedY = $y + $this->imageVerticalOffset;

        // 将图片也添加到HTML缓冲区中，而不是立即输出
        $style = "position: absolute; left: {$x}mm; top: {$adjustedY}mm; z-index: 1;";
        if ($w > 0) {
            $style .= " width: {$w}mm;";
        }
        if ($h > 0) {
            $style .= " height: {$h}mm;";
        }

        // 将图片HTML添加到缓冲区
        $this->htmlBuffer .= '<img src="' . $file . '" style="' . $style . '" />';

        // 不改变任何位置信息，不影响文档流
    }
    
    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->tableWidths = $widths;
    }
    
    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        if (is_string($aligns)) {
            // 如果是字符串，转换为数组
            $alignArray = [];
            for ($i = 0; $i < count($this->tableWidths); $i++) {
                $alignArray[] = $aligns;
            }
            $this->tableAligns = $alignArray;
        } else {
            $this->tableAligns = $aligns;
        }
    }
    
    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->tableHeight = $height;
    }
    
    /**
     * 绘制表格行
     */
    public function Row($data)
    {
        if (empty($this->tableWidths)) {
            return;
        }

        $html = '<table style="width: 100%; border-collapse: collapse;">';
        $html .= '<tr>';

        for ($i = 0; $i < count($data); $i++) {
            $width = isset($this->tableWidths[$i]) ? $this->tableWidths[$i] : 20;
            $align = isset($this->tableAligns[$i]) ? strtolower($this->tableAligns[$i]) : 'left';

            if ($align === 'c' || $align === 'center') {
                $align = 'center';
            } elseif ($align === 'r' || $align === 'right') {
                $align = 'right';
            } else {
                $align = 'left';
            }

            $cellData = isset($data[$i]) ? $data[$i] : '';

            // 使用安全的编码转换
            $cellData = $this->safeEncodeText($cellData);

            $html .= '<td style="width: ' . $width . 'mm; text-align: ' . $align . '; border: 1px solid #000; padding: 2px; height: ' . $this->tableHeight . 'mm;">';
            $html .= htmlspecialchars($cellData, ENT_QUOTES, 'UTF-8');
            $html .= '</td>';
        }

        $html .= '</tr>';
        $html .= '</table>';

        $this->mpdf->WriteHTML($html);
        $this->currentY += $this->tableHeight;
    }
    
    /**
     * 输出PDF
     */
    public function Output($name = '', $dest = 'I')
    {
        // 在输出前刷新HTML缓冲区
        $this->flushHtmlBuffer();

        // 确保文件名编码正确
        if (!empty($name)) {
            $name = $this->safeEncodeText($name);
        }

        // 设置正确的HTTP头
        if ($dest === 'I') {
            // 浏览器内联显示
            if (!headers_sent()) {
                header('Content-Type: application/pdf; charset=UTF-8');
                header('Content-Disposition: inline; filename="' . $name . '"');
                header('Cache-Control: private, max-age=0, must-revalidate');
                header('Pragma: public');
            }
        } elseif ($dest === 'D') {
            // 强制下载
            if (!headers_sent()) {
                header('Content-Type: application/pdf; charset=UTF-8');
                header('Content-Disposition: attachment; filename="' . $name . '"');
                header('Cache-Control: private, max-age=0, must-revalidate');
                header('Pragma: public');
            }
        }

        return $this->mpdf->Output($name, $dest);
    }
    
    /**
     * 设置自动分页
     */
    public function SetAutoPageBreak($auto, $margin = 0)
    {
        // mPDF自动处理分页
        return true;
    }

    /**
     * 获取字符串宽度
     */
    public function GetStringWidth($s)
    {
        $s = $this->safeEncodeText($s);
        return $this->mpdf->GetStringWidth($s);
    }


}

/**
 * 增强的PDF_Chinese类
 * 继承UniversalPdfGenerator，添加原有系统需要的特定功能
 */
class PDF_Chinese extends UniversalPdfGenerator
{
    // 表格相关属性
    private $widths = [];
    private $height = 6;
    private $aligns = [];
    private $valigns = [];
    private $fills = [];
    private $innerBorderX = true;
    private $innerBorderY = true;
    private $doBorder = true;
    private $doCalculateHeight = true;
    private $zeilenhoehe = 4.5;

    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        parent::__construct($orientation, $unit, $size);
    }

    /**
     * 兼容原有的AddCIDFont方法
     */
    public function AddCIDFont($family, $style, $name, $cw, $CMap, $registry)
    {
        // mPDF自动处理CID字体，这里保持兼容性
        return true;
    }

    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->widths = $widths;
        parent::SetWidths($widths);
    }

    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        $this->aligns = is_array($aligns) ? $aligns : [$aligns];
        parent::SetAligns($aligns);
    }

    /**
     * 设置垂直对齐方式
     */
    public function SetValigns($valigns)
    {
        $this->valigns = is_array($valigns) ? $valigns : [$valigns];
    }

    /**
     * 设置填充色
     */
    public function SetFills($fills)
    {
        $this->fills = is_array($fills) ? $fills : [$fills];
    }

    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->zeilenhoehe = $height;
        $this->height = $height;
        parent::SetZeilenhoehe($height);
    }

    /**
     * 增强的表格行绘制
     */
    public function Row($data)
    {
        // 使用父类的安全编码转换
        parent::Row($data);
    }

    /**
     * 兼容原有的Cell方法
     */
    public function Cell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        // 使用安全编码转换
        $txt = parent::safeEncodeText($txt);

        // 使用HTML表格模拟Cell功能
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }

        $alignStyle = '';
        if ($align) {
            $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right'];
            $alignStyle = 'text-align: ' . (isset($alignMap[$align]) ? $alignMap[$align] : 'left') . ';';
        }

        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }

        $html = '<table style="width: ' . $w . 'mm; height: ' . $h . 'mm; border-collapse: collapse;">';
        $html .= '<tr><td style="' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px;">';
        $html .= htmlspecialchars($txt, ENT_QUOTES, 'UTF-8');
        $html .= '</td></tr></table>';

        $this->mpdf->WriteHTML($html);

        if ($ln == 1) {
            $this->Ln($h);
        } elseif ($ln == 2) {
            $this->SetXY($this->leftMargin, $this->GetY() + $h);
        }
    }

    /**
     * 多字节字符串写入
     */
    public function MBWrite($h, $txt, $link = '')
    {
        return parent::Write($h, $txt, $link);
    }

    /**
     * 多字节字符串单元格
     */
    public function MBCell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        return $this->Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }

    /**
     * 多字节字符串多行输出
     */
    public function MBMultiCell($w, $h, $txt, $border = 0, $align = 'J', $fill = false)
    {
        // 使用安全编码转换
        $txt = parent::safeEncodeText($txt);

        // 使用HTML实现多行文本
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }

        $alignStyle = '';
        $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right', 'J' => 'justify'];
        if (isset($alignMap[$align])) {
            $alignStyle = 'text-align: ' . $alignMap[$align] . ';';
        }

        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }

        $html = '<div style="width: ' . $w . 'mm; ' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px; line-height: ' . $h . 'mm;">';
        $html .= nl2br(htmlspecialchars($txt, ENT_QUOTES, 'UTF-8'));
        $html .= '</div>';

        $this->mpdf->WriteHTML($html);
    }

    /**
     * 获取字符串宽度
     */
    public function GetStringWidth($s)
    {
        return parent::GetStringWidth($s);
    }

    /**
     * 设置文本颜色
     */
    public function SetTextColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            // 灰度模式
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            // RGB模式
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetTextColor($color);
    }

    /**
     * 设置绘图颜色
     */
    public function SetDrawColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetDrawColor($color);
    }

    /**
     * 设置填充颜色
     */
    public function SetFillColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetFillColor($color);
    }

    /**
     * 画矩形
     */
    public function Rect($x, $y, $w, $h, $style = '')
    {
        $this->mpdf->Rect($x, $y, $w, $h, $style);
    }

    /**
     * 设置页边距
     */
    public function SetMargins($left, $top, $right = null)
    {
        if ($right === null) {
            $right = $left;
        }
        $this->leftMargin = $left;
        $this->topMargin = $top;
        $this->rightMargin = $right;
        $this->mpdf->SetMargins($left, $right, $top);
    }

    /**
     * 设置左边距
     */
    public function SetLeftMargin($margin)
    {
        $this->leftMargin = $margin;
        $this->mpdf->SetLeftMargin($margin);
    }

    /**
     * 设置上边距
     */
    public function SetTopMargin($margin)
    {
        $this->topMargin = $margin;
        $this->mpdf->SetTopMargin($margin);
    }

    /**
     * 设置右边距
     */
    public function SetRightMargin($margin)
    {
        $this->rightMargin = $margin;
        $this->mpdf->SetRightMargin($margin);
    }
}
