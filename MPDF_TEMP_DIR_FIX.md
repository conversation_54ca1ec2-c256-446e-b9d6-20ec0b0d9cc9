# mPDF临时目录问题修复方案

## 问题描述

错误信息：
```
Fatal error: Uncaught Mpdf\MpdfException: Temporary files directory "/usr/local/www/diis/libs/vendor/mpdf/mpdf/src/Config/../../tmp/mpdf" is not writable
```

## 问题原因

mPDF库需要一个可写的临时目录来存储缓存文件和临时数据，但默认的临时目录没有写入权限。

## 解决方案

### 方案1：简化配置（已实施）

修改了`libs/util/pdf/UniversalPdfGenerator.php`，使用简化的mPDF配置：

```php
$config = [
    'mode' => 'utf-8',
    'format' => $size,
    'orientation' => $orientation,
    'margin_left' => $this->leftMargin,
    'margin_right' => $this->rightMargin,
    'margin_top' => $this->topMargin,
    'margin_bottom' => $this->bottomMargin,
];
```

### 方案2：手动创建临时目录（备用）

如果方案1不工作，可以手动创建临时目录：

```bash
# 在服务器上执行
mkdir -p /usr/local/www/diis/libs/util/pdf/tmp
chmod 755 /usr/local/www/diis/libs/util/pdf/tmp
chown www-data:www-data /usr/local/www/diis/libs/util/pdf/tmp
```

### 方案3：系统临时目录权限

确保系统临时目录可写：

```bash
# 检查临时目录权限
ls -la /tmp
# 如果需要，修改权限
chmod 1777 /tmp
```

## 测试验证

运行测试文件验证修复：

```bash
php test_mpdf_fix.php
```

如果成功，应该看到：
```
开始测试mPDF临时目录修复...
PDF实例创建成功！
页面添加成功！
PDF生成成功！临时目录问题已修复！
文件保存为: test_temp_fix.pdf
```

## 其他可能的解决方案

### 1. 环境变量设置

在PHP配置中设置临时目录：

```php
// 在系统初始化时设置
putenv('TMPDIR=/path/to/writable/temp');
```

### 2. mPDF配置文件

创建mPDF配置文件：

```php
// 在UniversalPdfGenerator.php中添加
$config['tempDir'] = '/path/to/writable/temp';
```

### 3. 服务器配置

在Apache/Nginx配置中设置：

```apache
# Apache
SetEnv TMPDIR /path/to/writable/temp
```

```nginx
# Nginx
fastcgi_param TMPDIR /path/to/writable/temp;
```

## 注意事项

1. **权限问题**：确保Web服务器用户（如www-data）对临时目录有读写权限
2. **磁盘空间**：确保临时目录所在磁盘有足够空间
3. **清理机制**：定期清理临时文件避免磁盘空间不足

## 故障排除

### 如果仍然出现权限错误：

1. 检查PHP进程用户：
```bash
ps aux | grep php
```

2. 检查目录权限：
```bash
ls -la /usr/local/www/diis/libs/util/pdf/
```

3. 手动创建并设置权限：
```bash
mkdir -p /usr/local/www/diis/libs/util/pdf/tmp
chown -R www-data:www-data /usr/local/www/diis/libs/util/pdf/tmp
chmod -R 755 /usr/local/www/diis/libs/util/pdf/tmp
```

### 如果是SELinux问题：

```bash
# 检查SELinux状态
sestatus

# 如果启用了SELinux，设置上下文
setsebool -P httpd_can_network_connect 1
chcon -R -t httpd_exec_t /usr/local/www/diis/libs/util/pdf/tmp
```

## 验证修复

修复后，原有的PDF生成代码应该能正常工作：

```php
$pdf = new PDF_Chinese();
$pdf->AddPage();
$pdf->SetFont('DejaVuSans', '', 12);
$pdf->Write(10, 'Test');
$pdf->Output('test.pdf', 'I');
```

如果还有问题，请检查PHP错误日志获取更详细的错误信息。
