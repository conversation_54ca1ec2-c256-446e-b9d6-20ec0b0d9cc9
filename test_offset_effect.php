<?php
/**
 * 测试偏移量是否生效
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试偏移量效果...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 创建测试图片
    $testImagePath = 'offset_test.png';
    if (!file_exists($testImagePath)) {
        $image = imagecreate(12, 12);
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        $border_color = imagecolorallocate($image, 255, 0, 0);
        
        // 画一个红色边框的方框
        imagerectangle($image, 0, 0, 11, 11, $border_color);
        imagerectangle($image, 1, 1, 10, 10, $border_color);
        
        imagepng($image, $testImagePath);
        imagedestroy($image);
        echo "测试图片创建成功！\n";
    }
    
    // 设置字体
    $pdf->SetFont('simsun', '', 12);
    
    echo "测试模拟实际代码的调用方式...\n";
    
    // 模拟实际代码中的调用方式
    $x8 = 50;
    $y8 = $pdf->GetY(); // 这是实际代码中的方式
    echo "当前Y位置: $y8\n";
    echo "设置的偏移量: " . $pdf->imageVerticalOffset . "mm\n";
    
    $pdf->Write(10, '测试文字：');
    $pdf->Image($testImagePath, $x8, $y8, 8, 8); // 使用GetY()获取的位置
    $pdf->Write(10, ' 图片应该向下偏移');
    $pdf->Ln();
    
    // 再测试一次，使用不同的Y值
    $x9 = 50;
    $y9 = $pdf->GetY();
    echo "第二次Y位置: $y9\n";
    
    $pdf->Write(10, '第二行测试：');
    $pdf->Image($testImagePath, $x9, $y9, 8, 8);
    $pdf->Write(10, ' 这里也应该有偏移');
    $pdf->Ln();
    
    // 测试修改偏移量
    $pdf->SetImageVerticalOffset(2.0); // 设置2mm偏移
    $x10 = 50;
    $y10 = $pdf->GetY();
    echo "修改偏移量为2mm，Y位置: $y10\n";
    
    $pdf->Write(10, '偏移2mm测试：');
    $pdf->Image($testImagePath, $x10, $y10, 8, 8);
    $pdf->Write(10, ' 偏移量更大');
    $pdf->Ln();
    
    // 输出PDF
    $pdf->Output('test_offset_effect.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: test_offset_effect.pdf\n";
    echo "请检查PDF文件，确认图片是否向下偏移了。\n";
    echo "如果图片位置正确，说明偏移量生效了。\n";
    
    // 清理测试图片
    if (file_exists($testImagePath)) {
        unlink($testImagePath);
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
