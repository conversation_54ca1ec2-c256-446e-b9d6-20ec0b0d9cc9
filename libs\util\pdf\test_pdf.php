<?php
/**
 * PDF系统测试文件
 */

// 模拟LIB_DIR常量（在实际使用中这个常量已经定义）
if (!defined('LIB_DIR')) {
    define('LIB_DIR', dirname(dirname(__DIR__)));
}

require_once LIB_DIR . '/util/pdf/classfile.php';

try {
    echo "开始测试PDF生成...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese('P', 'mm', 'A4');
    
    // 添加页面
    $pdf->AddPage();
    
    // 设置字体
    $pdf->SetFont('simsun', '', 14);
    
    // 写入测试文本
    $pdf->Write(10, '测试中文PDF生成功能');
    $pdf->Ln();
    $pdf->Write(10, 'Test Chinese PDF Generation');
    $pdf->Ln();
    $pdf->Write(10, '这是一个测试文档，用于验证mPDF库的中文支持。');
    $pdf->Ln();
    
    // 测试表格功能
    $pdf->SetWidths([50, 50, 50]);
    $pdf->SetAligns(['C', 'C', 'C']);
    $pdf->Row(['列1', '列2', '列3']);
    $pdf->Row(['数据1', '数据2', '数据3']);
    
    // 输出PDF
    $pdf->Output('test_chinese.pdf', 'F');
    
    echo "PDF生成成功！文件保存为: test_chinese.pdf\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
