<?php
/**
 * 简单PDF测试
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始简单PDF测试...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddPage();
    
    echo "PDF实例创建成功！\n";
    
    // 使用最基本的方法写入文本
    $pdf->SetFont('DejaVuSans', '', 12);
    
    // 直接使用mPDF的WriteHTML方法
    $pdf->mpdf->WriteHTML('<p>测试中文PDF生成</p>');
    $pdf->mpdf->WriteHTML('<p>Test English Text</p>');
    $pdf->mpdf->WriteHTML('<p>混合文本 Mixed Content</p>');
    
    echo "内容添加成功！\n";
    
    // 输出为字符串检查
    $pdfString = $pdf->Output('simple_test.pdf', 'S');
    
    // 检查PDF内容是否有效
    if (substr($pdfString, 0, 4) === '%PDF') {
        echo "✓ PDF格式正确\n";
        echo "PDF大小: " . strlen($pdfString) . " 字节\n";
        
        // 保存到文件
        file_put_contents('simple_test.pdf', $pdfString);
        echo "✓ PDF文件保存成功: simple_test.pdf\n";
        
    } else {
        echo "✗ PDF格式错误\n";
        echo "前50字节: " . bin2hex(substr($pdfString, 0, 50)) . "\n";
        echo "前50字符: " . substr($pdfString, 0, 50) . "\n";
    }
    
    echo "\n简单PDF测试完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
