<?php    
require_once (APP_DIR . "/master/MasterAction.inc.php");

$GLOBALS['PAYTYPE'] = array(
    //"1"=>"线下支付",
	"1"=>"先款后货",
	"4"=>"先货后款",
	);
$GLOBALS['DELIVERY'] = array(
     "1"=>"自提",
	 "2"=>"代办"
);
$GLOBALS['ZFKXS'] = array(
    "1"=>"在线支付",
	"2"=>"银行汇款",
	"3"=>"现金",
    "4"=>"银行承兑",
	"5"=>"企业承兑",
    "6"=>"信用证",
    "7"=>"银行担保",
	"8"=>"企业担保",
	"9"=>"自定义",
	"10"=>"铁建银信",
);
$GLOBALS['JIAOGE_TYPES'] = array(
    "1"=>"即时交割",
	"2"=>"指定日交割",
);
$GLOBALS['DANBAO_TYPES'] = array(
   // "0"=>"无担保",
	//	"1"=>"建行E商贸通监管",
	//	"2"=>"中铁物资集团担保",
	 	"3"=>"钢之家超越资金监管",
);




class PdfAction extends MasterAction
{
    public function __construct()
    {
        parent::__construct();
    } 

    public function checksession()
    {
        if ($_SESSION['SYS_COMPANYID'] == '')
        {
            goURL("index.php?view=login");
            exit;
        } 
    } 

    function pdfStrCut($str = '', $rowLen = 0)
    {
        // 使用新的编码转换工具
        require_once(LIB_DIR . "/util/pdf/PdfAdapter.php");
        return PdfAdapter::pdfStrCut($str, $rowLen, 'UTF-8');
    }

    /**
     * 通用的addline方法，避免重复定义
     */
    function addline($txt1, $txt2, $txt3, $pdf)
    {
        $pdf->SetFont('simsun','', 12);
        $pdf->Write(10, $txt1);
        $pdf->SetFont('kaiti', 'U', 12); //设置字体样式
        $pdf->Write(10, $txt2);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, $txt3);
    }

    function setvars()
    {
        $this->assign("jgtypes", $GLOBALS['JIAOGE_TYPES']);
        $this->assign("dbtypes", $GLOBALS['DANBAO_TYPES']);
        $gcs = $this->_dao->AQuery("SELECT OriginCode, OriginNameShort FROM sm_base_placeorigin");
        $pzs = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety_big Where 1=1 ORDER BY ID");
        $zmds = $this->_dao->AQuery("SELECT MID, StoreShortName FROM sm_sys_storecustom ORDER BY StoreOrder ");
        $citys = $this->_dao->AQuery("SELECT CityCode, CityName FROM sm_sys_storecity Where 1=1 ORDER BY ID");
        $pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");

        $cz = $this->_dao->AQuery("SELECT MaterialCode,MaterialName  FROM sm_base_material Where 1=1 ORDER BY ID");
        $gg = $this->_dao->AQuery("SELECT SpecCode,SpecName  FROM sm_base_specification ");

        $this->assign("cz", $cz);
        $this->assign("gg", $gg);

        $this->assign("pzs2", $pzs2);
        $this->assign("units", $GLOBALS['UNIT_TYPES']);
        $this->assign("zmds", $zmds);
        $this->assign("pzs", $pzs);
        $this->assign("citys", $citys);
        $this->assign("gcs", $gcs);
        $this->assign("storetypes", $GLOBALS['STORE_TYPES']);
        $this->assign("zystatus", $GLOBALS['ZY_STATUS']);
        $this->assign("jjstatus", $GLOBALS['JINJIA_STATUS']);
        $this->assign("htstatus", $GLOBALS['HT_STATUS']);
        $this->assign("jgdtypes", $GLOBALS['TRANSACTION_STATUS']);
        $this->assign("jgdstatus", $GLOBALS['JGD_STATUS']);
        $this->assign("jgdbuystatus", $GLOBALS['JGD_BUY_STATUS']);
        $this->assign("jgdsalestatus", $GLOBALS['JGD_SALE_STATUS']);
        $this->assign("zysaletypes", $GLOBALS['ZY_SALE_TYPES']);
		$this->assign("paytype",$GLOBALS['PAYTYPE']);  //付款方式
		$this->assign("fkxs",$GLOBALS['ZFKXS']);
        $this->assign("Delivery", $GLOBALS['DELIVERY']); //提货方式
        
    } 

    public function index($params)
    {
        $hth = $params['hth'];




        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");

        if($contact['shipper_level']=='2'){
            //大宗C类客户走C类模板
            gourl("pdf.php?action=index_c_level&hth=".$hth);
            exit;
        }

        include("classfile.php");
        include("phpqrcode.php");

        $contact['yzf_Shipper'] = explode(",",$contact['yzf_Shipper']);
        $contact['yzf_Consignee'] = explode(",",$contact['yzf_Consignee']);

        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $hth . "'");
        //$zydetail = $this->zy_format($zydetail);
        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
        //updated by hzp ,竞价也当协议价20150806 started
        if($zy['bjfs']=="2" || $zy['bjfs'] == "3"){
        $zy['bjfs'] = "3";
        }
        //updated by hzp ,竞价也当协议价20150806 end
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($buycomp['signCompanyname'] !=""){
            $buycomp['ComName'] = $buycomp['signCompanyname'];
        }else{
            $buycomp['ComName'] = $buycomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($salecomp['signCompanyname'] !=""){
            $salecomp['ComName'] = $salecomp['signCompanyname'];
        }else{
            $salecomp['ComName'] = $salecomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->AddGBFont('kaiti', '华文楷体');

        $pdf->Open();
        $pdf->AddPage();
        
        
        // XK start deleted for login 2014/11/28
        //$pdf->SetAutoPageBreak(true);
        // XK end deleted for login 2014/11/28
        
        
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        
        $pdf->SetX(10);
        $pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 130, 10,70,8);
        // update by libing end for barcode in 2015/08/07

        $pdf->Line(10, 20, 200, 20);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(80);
        $pdf->SetFont('simsun', 'B', 14);
        $pdf->Write(9, '工矿产品交易合同');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10, '甲方（出卖方）：');
        $pdf->Write(10, $salecomp['ComName']);
        
        $pdf->SetX(130);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10,'签订地点：');
        $pdf->Write(10, $contact['qdaddress']);
        $pdf->Ln();


        $pdf->SetX(10);
        $pdf->Write(10, '乙方（买受方）：');
        $pdf->Write(10, $buycomp['ComName']);

        $pdf->SetX(130);
        $pdf->Write(10,'签订时间：');
        $pdf->Write(10, $contact['qdtime']);
        $pdf->Ln();
        $pdf->SetX(20);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '根据《中华人民共和国合同法》及其他有关法律、法规的规定，买卖双方在平等、自愿、公平、诚实信用的基础上就产品买卖事宜达成协议如下：');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第一条 产品名称、材质、规格、生产厂家、数量、含税单价、金额：');

        //Updated by quanjw for meijiao start 2015/7/13
        $pdf->Ln();
        $pdf->SetX(10);
        //$pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', '', 10); //设置字体样式
        $pdf->SetAligns("center");
        
        
        /*$pdf->Row(array('产品名称', '材质', '规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
        foreach($zydetail as $v)
        {   $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where Dtid    ='".$v['BID']."' and Sid = '".$v['Sid']."' ",0);
            $dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', '', 11); //设置字体样式
            $pdf->SetAligns("center");

            $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
        } 
        */
        
        //资源分类
        $zyll = $this->groupResourse($zydetail);
        
        foreach($zyll as $k => $res)
        {
            //输出分类资源标题
            if( $k == $GLOBALS["MEITAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '挥发分', '灰分','全硫分', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '灰分', '全硫分','CSR', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["SHUINI_VID"]){
                $pdf->SetWidths(array(36, 36, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称','规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["JINSHU_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '规格', '强度', '锌层重量', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["TKS_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', 'Fe%', 'SiO2%', 'AL2O3%', '产地', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名     
            }else{
                $pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '材质', '规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }
            foreach($res as $v){
                //输出资源详细信息
                //added by quanjw start
                if($v['ddid'] == "" || $v['ddid'] == "0"){
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where Dtid    ='".$v['BID']."' and Sid = '".$v['Sid']."' ",0);
                }else{
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where id    ='".$v['ddid']."'",0);
                }
                //added by quanjw end
                
                $dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
                $pdf->SetZeilenhoehe("6");
                $pdf->SetFont('simsun', '', 11); //设置字体样式
                $pdf->SetAligns("center");
                if( $k == $GLOBALS["MEITAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['cd'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['MaterialCode'], $v['cd'],  $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["SHUINI_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JINSHU_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['strength'], $v['xincengWeight'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   //added by shizg started 2016/09/21 
                }elseif( $k == $GLOBALS["TKS_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['tks_fe'], $v['tks_si'], $v['tks_al'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   
                   //added by shizg ended 2016/09/21 
                }else{
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }
            }
            $pdf->Ln();
        } 
        //Updated by quanjw for meijiao start 2015/7/13
        
        $pdf->SetWidths(array(189)); // 设置每列的宽度
        $pdf->Row(array('合计人民币金额（大写）：'. $this->cny($contact['TotalMoney']) ) ); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        $pdf->Write(9, '注：1、单价仅作为签订合同参考价格。');
        $pdf->Ln();
        $pdf->SetX(17);
        $pdf->Write(9, '2、具体交易数量以实际交付为准，以单价乘以实际交付数量为交易金额。');
        $pdf->Ln();
        $pdf->SetX(17);
        $pdf->Write(9, '3、如果没有填写具体交易商品明细，则见附件。');

        // 使用类方法addline替代重复的函数定义

        $pdf->Ln();
        $pdf->SetX(10);

        $this->addline('第二条 产品质量要求：货物质量标准按 ',   "   ".$contact['zlbz']."   "  , ' 标准执行。', $pdf);

        $pdf->Ln();

        addline('第三条 包装标准、包装物的供应与回收：', "   ".$contact['bzbz']."   ", '。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第四条 交货日期和交货地点及收货单位');

        $pdf->Ln();
        // $pdf->SetX(10);
        // $pdf->SetFont('simsun','',14); //设置字体样式
        // $pdf->Write(10,'（一）指定交易地：____________________。');
        addline('（一）最迟交货日期：', "   ".$contact['PickUpDate']."   ", '。', $pdf);

        $pdf->Ln();
        // $pdf->SetX(10);
        // $pdf->SetFont('simsun','',14); //设置字体样式
        // $pdf->Write(10,'（二）最迟交货期：____________________。');
        addline('（二）交货地点：', "   ".$contact['PickUpAddress']."   ", '。', $pdf);

        $pdf->Ln();
        // $pdf->SetX(10);
        // $pdf->SetFont('simsun','',14); //设置字体样式
        // $pdf->Write(10,'（三）货物运输：出库费、运杂费、保险费、装卸吊装费及保管费等与运输有关的费用均由_______承担。');
        addline('（三）收（提）货单位：', "   ".$buycomp['ComName']."   ", '。', $pdf);
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第五条 运输方式、费用和交付条件');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（一）运输方式：');
        
        $pdf->SetFont('simsun', '', 12);
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     自提 ');
        
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     代运（');


        $pdf->SetFont('simsun', 'U', 12);
        $x5 = $pdf->GetX();
        $y5 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x5, $y5);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x5, $y5);
        } 
        $pdf->Write(10, '       汽车运输、');
        
        $x6 = $pdf->GetX();
        $y6 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x6, $y6);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x6, $y6);
        } 
        $pdf->Write(10, '       火车运输、');
        
        $x7 = $pdf->GetX();
        $y7 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x7, $y7);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x7, $y7);
        } 
        $pdf->Write(10, '       水运）。');

        //delete by hezp started 2015/09/29
        /*$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第五条 运输方式、费用和交付条件');*/
        //delete by hezp ended 2015/09/29

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（二）如果  ');
        
        $pdf->SetFont('simsun', '', 12);
        $x9 = $pdf->GetX();
        $y9 = $pdf->GetY();
        if (in_array("1",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x9, $y9);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x9, $y9);
        } 
        $pdf->Write(10, '       运杂费、');

        $pdf->SetFont('simsun', '', 12);
        $x10 = $pdf->GetX();
        $y10 = $pdf->GetY();
        if (in_array("2",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x10, $y10);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x10, $y10);
        } 
        $pdf->Write(10, '       出库费、');
        
        $x11 = $pdf->GetX();
        $y11 = $pdf->GetY();
        if (in_array("3",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x11, $y11);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x11, $y11);
        } 
        $pdf->Write(10, '       装卸吊装费、');
        
        $x12 = $pdf->GetX();
        $y12 = $pdf->GetY();
        if (in_array("4",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x12, $y12);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x12, $y12);
        } 
        $pdf->Write(10, '       保管费、');

        $x13 = $pdf->GetX();
        $y13 = $pdf->GetY();
        if (in_array("5",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x13, $y13);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x13, $y13);
        } 
        $pdf->Write(10, '       保险费等保险费等与物流有关的费用由乙方承担，开具发票为两票制；如果  ');

        $pdf->SetFont('simsun', '', 12);
        $x14 = $pdf->GetX();
        $y14 = $pdf->GetY();
        if (in_array("1",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x14, $y14);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x14, $y14);
        } 
        $pdf->Write(10, '       运杂费、');

        $pdf->SetFont('simsun', '', 12);
        $x15 = $pdf->GetX();
        $y15 = $pdf->GetY();
        if (in_array("2",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x15, $y15);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x15, $y15);
        } 
        $pdf->Write(10, '       出库费、');
        
        $x16 = $pdf->GetX();
        $y16 = $pdf->GetY();
        if (in_array("3",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x16, $y16);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x16, $y16);
        } 
        $pdf->Write(10, '       装卸吊装费、');
        
        $x17 = $pdf->GetX();
        $y17 = $pdf->GetY();
        if (in_array("4",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x17, $y17);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x17, $y17);
        } 
        $pdf->Write(10, '       保管费、');

        $x18 = $pdf->GetX();
        $y18 = $pdf->GetY();
        if (in_array("5",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x18, $y18);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x18, $y18);
        } 
        $pdf->Write(10, '       保险费等保险费等与物流有关的费用由甲方承担，开具发票为一票制。');
 
        /*$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun','',12);
        addline('（二）如果运杂费、出库费、装卸吊装费、保管费及保险费等与物流有关的费用均由', "  乙方   ", '承担，开具发票为两票制。', $pdf);
        
        addline('如果运杂费、出库费、装卸吊装费、保管费及保险费等与物流有关的费用均由', "  甲方  ", '承担，开具发票为一票制。', $pdf);*/
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun','',12); //设置字体样式
        addline('（三）如果乙方凭甲方发出的提货单提取货物，甲方发出《提货单》前，乙方应将指定收货人的姓名、身份证号码，车船号等信息书面通知甲方。如果乙方在提货单记载的交货日期内提取货物，交付货物时甲方应提供磅码单、货物出厂质量证明书等货物随行相关证明一式',"   ".$contact['xgzs']."   ",'份。' , $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（四）货物交付给乙方之前，货物的损毁，灭失和失窃的风险由甲方承担。货物交付给乙方或乙方指定的提货人在提货单或验收单上签字，双方确认签收后即视为货权转移标志。');
   
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第六条 货物验收');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）数量确认：乙方提取货物时按：以（ ');

        $x3 = $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['hwsl'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
        } 
        $pdf->Write(10, '       过磅  ');

        $x4 = $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['hwsl'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '       理计）确认接收货物数量。由乙方或乙方指定的提货人或第一承运人在提货单中签字或盖章确认。交货数量按本合同约定货物数量的±5% 的范围控制。最终数量依磅单为准，超出此有效交易量，双方可以协商处理。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）质量检验：乙方按约定的质量标准进行验收，在货物交付后15日内向甲方提出书面异议，由双方协商解决。乙方逾期未提出书面异议的，视为乙方已认可货物质量符合本合同约定。');

        $pdf->Ln();
        // $pdf->SetX(10);
        // $pdf->SetFont('simsun','',14); //设置字体样式
        // $pdf->Write(10,'（三）发票开具：甲方收款后_____个工作日内按乙方付款金额开具等额有效的发票，发票记载的货物品名、规格型号等信息必须与本合同约定一致，否则乙方可要求甲方重开，因此产生的费用由甲方承担。');
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（三）发票开具：甲方按开具双方实际结算的金额开具等额有效发票，发票记载的货物品名、规格型号等信息必须与本合同约定一致，否则乙方可要求甲方重开，因此产生的费用由甲方承担。货权转移确认后，甲方应在15日内给乙方开具税务发票。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第七条 结算方式及付款期限');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）结算方式： ');
        $x2 = $pdf->GetX();
        $y2 = $pdf->GetY();
        if ($contact['PayType']=="1")
        {
            $pdf->Image("images/gou1.jpg", $x2, $y2);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x2, $y2);
        } 
        $pdf->Write(10, '       先款后货、');

        $x = $pdf->GetX();
        $y = $pdf->GetY();
        if ($contact['PayType'] == "4")
        {
            $pdf->Image("images/gou1.jpg", $x, $y);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x, $y);
        } 
        $pdf->Write(10, '       先货后款。');
        //update by hzp for zhangdandan xuqiu started 2015/08/19
        //加个判断先货后款后面不显示
        if($contact['PayType'] == "4"){
            addline('以'.$GLOBALS['PAYTYPE'][$contact['PayType']].'方式交易。','','',$pdf);
        }else{
            addline('以'.$GLOBALS['PAYTYPE'][$contact['PayType']].'方式交易，乙方在本合同签订后的', "   ".$contact['fkqx']."   ", '日内',$pdf);
            addline('付清', "   ".$contact['lybzj']."   ", '%货款到甲方帐户。',$pdf);
            addline('甲方在收到', "   ".$contact['lybzj']."   ", '%货款后',$pdf);
            addline('的', "   ".$contact['jhqx']."   ", '日内，按照要求将货物送至指定的交货地点。',$pdf);
        }
        //added by hzp for zhangdandan xuqiu ended 2015/08/19
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）付款方式： ');
        $x = $pdf->GetX();
        $y = $pdf->GetY();
        if ($contact['fkxs'] == "1" || $contact['fkxs'] == "2" || $contact['fkxs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x, $y);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x, $y);
        } 
        $pdf->Write(10, '       银行汇款、 ');

        $x2 = $pdf->GetX();
        $y2 = $pdf->GetY();

        if ($contact['fkxs'] == "4")
        {
            $pdf->Image("images/gou1.jpg", $x2, $y2);
            addline('       ', "  ".$contact['cdhp']."  ", '天银行承兑汇票、',$pdf);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x2, $y2);
            addline('       ', "  ", '天银行承兑汇票、',$pdf);
        } 
        
        $x3= $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['fkxs'] == "5")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
            addline('       ', "  ".$contact['cdhp']."  ", '天商业承兑汇票、',$pdf);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
            addline('       ', "  ", '天商业承兑汇票、',$pdf);
        }

        $x4= $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['fkxs'] == "6")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '       国内信用证、');

        $x5 = $pdf->GetX();
        $y5 = $pdf->GetY();
        if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8" || $contact['fkxs'] == "9"  || $contact['fkxs'] == "10")
        {
            $pdf->Image("images/gou1.jpg", $x5, $y5);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x5, $y5);
        }
        if ($contact['fkxs'] == "10")
        {
        $tempfkxs=$contact['cdhp']."天".$GLOBALS['ZFKXS'][$contact['fkxs']];
        addline('       其他', "      ".$tempfkxs."      ", '，',$pdf);
        }
        else if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8")
        {
        $tempfkxs=$GLOBALS['ZFKXS'][$contact['fkxs']];
        $tempfkxs=$tempfkxs."（".$contact['dwname']."）";
        addline('       其他', "      ".$tempfkxs."      ", '，',$pdf);
        }
        else{
        addline('       其他', "      ".$contact['dwname']."      ", '，',$pdf);  
        }
        addline('由此所产生的贴息费用等相关费用由', "   ".$GLOBALS['HTSF_2'][$contact['txcd']]."   ", '承担。', $pdf);
        
        $pdf->Ln();
        // $pdf->SetX(10);
        // $pdf->SetFont('simsun','',14); //设置字体样式
        // $pdf->Write(10,'（三）交易双方商定本合同的履约保证金为______________，用以补偿守约方的损失。');
        addline('（三）交易双方商定本合同的交易保证金为货款的', "   ".$contact['yqwyj']."   ", '‰，用以补偿守约方的损失，', $pdf);
        addline('交易保证金由', "   ".$GLOBALS['HTSF_2'][$contact['txcd']]."   ", '承担，在本合同签订后1日内支付。', $pdf);

        $pdf->Ln();
        addline('（四）履约保证金：甲乙双方为了保证及时支付货款或保证货物及时安全交付，甲方和乙方同意按货款', "   ".$contact['ycfkwyj']."   ", '‰支付履约保证金，', $pdf);
        addline('由', "   ".$GLOBALS['HTSF_2'][$contact['ysfy']]."   ", '方在1日内支付。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（五）结算价格确认： ');
        $x19 = $pdf->GetX();
        $y19 = $pdf->GetY();
        if ($zy['bjfs']=="1")
        {
            $pdf->Image("images/gou1.jpg", $x19, $y19);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x19, $y19);
        } 
        $pdf->Write(10, '       锁定价格，则本合同签订的货物单价作为结算价格，不可更改。');

        $x20 = $pdf->GetX();
        $y20 = $pdf->GetY();
        if ($zy['bjfs']== "3")
        {
            $pdf->Image("images/gou1.jpg", $x20, $y20);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x20, $y20);
        } 
        $pdf->Write(10, '       协议价格，则本合同签订的货物单价允许进行浮动修改，结算价格由双方最终在结算环节中进行确定。');



        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第八条 违约责任');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）甲方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、甲方交付的产品：品种、型号、规格和质量等不符合本合同规定的，如果乙方同意使用，应当另行协商；如果乙方不能使用或不同意使用的，甲方应无条件换货、退货并承担相应费用或赔偿乙方由此受到的经济损失。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2、甲方无正当理由单方解除合同的，应立即退还乙方支付的任何形式资金应赔偿由此给乙方造成的经济损失。');

        //$pdf->Ln();
        //addline('2、卖方迟延交货的，每日应向买方支付迟延部分价款', "   ".$contact['yqwyj']."   ", '‰的违约金；', $pdf);
        //addline('迟延交货超过', "   ".$contact['yqqx']."   ", '日的，除支付违约金外，买方还有权解除合同，卖方已收取的定金、预付款或价款应全额返还，但买方在不收取违约金的情况下有权要求卖方双倍返还定金。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）乙方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、乙方无正当理由单方解除合同的，应赔偿由此给甲方造成的经济损失，已支付的履约保证金无权要求返还。', $pdf);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第九条 争议的解决方式');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        //Update xiakang for pdf started 2015/08/29
        if($contact['DisputeSettlement']=="1"){
        $pdf->Write(10, '合同争议的解决方式：本合同项下发生的争议，由买卖双方协商解决或申请调解解决；协商或调解不成，按下列第（一）种方式解决：');
        }
        if($contact['DisputeSettlement']=="2"){
        $pdf->Write(10, '合同争议的解决方式：本合同项下发生的争议，由买卖双方协商解决或申请调解解决；协商或调解不成，按下列第（二）种方式解决：');
        }
    
        $pdf->Ln();
        //$pdf->Write(10, '（一）提交仲裁委员会仲裁。');
        addline('（一）提交'," ".$contact['DisputeSettlement_city']." ", '仲裁委员会仲裁。',$pdf);
        //Update xiakang for pdf ended 2015/08/29
        $pdf->Ln();

        $pdf->Write(10, '（二）向合同签订地人民法院起诉。');
        $pdf->Ln();
        $pdf->Write(10, '第十条 其他约定事项：如果任意一方提出合同条款及事项进行变更，则应提前 3 天书面通知对方，征得同意后，可签订书面协议，否则应承担违约责任');
        $pdf->Ln();

        $pdf->Write(10, '第十一条 本合同自双方签字盖章之日生效，共一式陆份，买卖双方各执叁份，具有同等法律效力。本合同在双方权利义务履行完毕后自行失效。');
        $pdf->Ln();
        //added by hzp started 2015/08/17
        if($contact['fjtk'] !=""){
        $pdf->Write(10, '第十二条 附加条款： '.$contact['fjtk'].'');
        $pdf->Ln();
        }
        //added by hzp ended 2015/08/17
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '甲方：');
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],0,18));
        $pdf->SetX(115);
        $pdf->Write(10, '乙方：');
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],0,16));
        $pdf->Ln();

    if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
        $pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
        $pdf->Ln();
    }
        $pdf->SetX(10);
        $pdf->Write(10, '法人代表：');
        $pdf->Write(10, $salecomp['Frdb']);

        $pdf->SetX(115);
        $pdf->Write(10, '法人代表：');
        $pdf->Write(10, $buycomp['Frdb']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $this->c_substr($salecomp['Address'],0,15));

        $pdf->SetX(115);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $this->c_substr($buycomp['Address'],0,13));
        $pdf->Ln();


    if(strlen($salecomp['Address']) > 30 || strlen($buycomp['Address']) > 26){
        $pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['Address'],15,strlen($salecomp['Address'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['Address'],13,strlen($buycomp['Address'])));
        $pdf->Ln();
    }


        $pdf->SetX(10);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $salecomp['ContactTel']);

        $pdf->SetX(115);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $salecomp['ContactFax']);

        $pdf->SetX(115);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($salecomp['BankType'],0,12));

        $pdf->SetX(115);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($buycomp['BankType'],0,11));
        $pdf->Ln();

    if(strlen($salecomp['BankType']) > 24 || strlen($buycomp['BankType']) > 22){
        $pdf->SetX(35);
        
        $pdf->Write(10, $this->c_substr($salecomp['BankType'],12,strlen($salecomp['BankType'])));

        $pdf->SetX(140);
        
        $pdf->Write(10, $this->c_substr($buycomp['BankType'],11,strlen($buycomp['BankType'])));
        $pdf->Ln();
    }

        $pdf->SetX(10);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $salebank['MBR_SPE_ACCT_NO']);

        $pdf->SetX(115);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $buybank['MBR_SPE_ACCT_NO']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $salecomp['TaxNo']);

        $pdf->SetX(115);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $buycomp['TaxNo']);
        $pdf->Ln();
        // $pdf->Uni_putpages();
        // 二维码
        $b = str_replace(' ', '', $contact['TwoDimCode']); 
        // $pdf->Write(10,$c);
        $c = WEB_SITE."/member.php?view=yzpdf&hid=" . $hth . "&dimcode=" . $b."&dtid=".$contact['BID'];

        QRcode::png($c, "images/ewm/" . $contact['ContractNo'] . ".png");

        $sc = urlencode($c);

        $xer = $pdf->GetX();
        $yer = $pdf->GetY();

        $pdf->Image("images/ewm/" . $contact['ContractNo'] . ".png", $xer, $yer);

        $pdf->SetXY($xer+8,$yer+49);
        $pdf->SetFont('simsun', '', 8);
        $pdf->Write($xer, '扫描二维码可辨别此合同真伪');

        $pdf->SetFont('simsun', '', 12);
        if($params['fromapp']=='1'){
            echo base64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }else{
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        }
        
    }

    //added by hezp started 2017/10/16
    /*集团公司各经营单位在中国大宗物资网进行交易，如交易的对方为C级供应商，则该交易必须使用C级供应商《工矿产品采购合同》进行签订*/
    public function index_c_level($params)
    {
        $hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        $contact['yzf_Shipper'] = explode(",",$contact['yzf_Shipper']);
        $contact['yzf_Consignee'] = explode(",",$contact['yzf_Consignee']);

        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $hth . "'");
        //$zydetail = $this->zy_format($zydetail);
        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
        //updated by hzp ,竞价也当协议价20150806 started
        if($zy['bjfs']=="2" || $zy['bjfs'] == "3"){
        $zy['bjfs'] = "3";
        }
        //updated by hzp ,竞价也当协议价20150806 end
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($buycomp['signCompanyname'] !=""){
            $buycomp['ComName'] = $buycomp['signCompanyname'];
        }else{
            $buycomp['ComName'] = $buycomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($salecomp['signCompanyname'] !=""){
            $salecomp['ComName'] = $salecomp['signCompanyname'];
        }else{
            $salecomp['ComName'] = $salecomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->AddGBFont('kaiti', '华文楷体');

        $pdf->Open();
        $pdf->AddPage();
        
        
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        
        $pdf->SetX(10);
        $pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 130, 10,70,8);
        // update by libing end for barcode in 2015/08/07

        $pdf->Line(10, 20, 200, 20);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(80);
        $pdf->SetFont('simsun', 'B', 14);
        $pdf->Write(9, '工矿产品采购合同');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10, '甲方（买受方）：');
        $pdf->Write(10, $buycomp['ComName']);
        
        $pdf->SetX(130);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10,'签订地点：');
        $pdf->Write(10, $contact['qdaddress']);
        $pdf->Ln();


        $pdf->SetX(10);
        $pdf->Write(10, '乙方（出卖方）：');
        $pdf->Write(10, $salecomp['ComName']);

        $pdf->SetX(130);
        $pdf->Write(10,'签订时间：');
        $pdf->Write(10, $contact['qdtime']);
        $pdf->Ln();
        $pdf->SetX(20);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '根据《中华人民共和国合同法》及其他有关法律、法规的规定，买卖双方在平等、自愿、公平、诚实信用的基础上就产品买卖事宜达成协议如下：');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第一条 产品名称、材质、规格、生产厂家、数量、含税单价、金额：');

        //Updated by quanjw for meijiao start 2015/7/13
        $pdf->Ln();
        $pdf->SetX(10);
        //$pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', '', 10); //设置字体样式
        $pdf->SetAligns("center");
        
        
        //资源分类
        $zyll = $this->groupResourse($zydetail);
        
        foreach($zyll as $k => $res)
        {
            //输出分类资源标题
            if( $k == $GLOBALS["MEITAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '挥发分', '灰分','全硫分', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '灰分', '全硫分','CSR', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["SHUINI_VID"]){
                $pdf->SetWidths(array(36, 36, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称','规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["JINSHU_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '规格', '强度', '锌层重量', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }elseif( $k == $GLOBALS["TKS_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', 'Fe%', 'SiO2%', 'AL2O3%', '产地', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名     
            }else{
                $pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '材质', '规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','交货日期')); // 设置列名    
            }
            foreach($res as $v){
                //输出资源详细信息
                //added by quanjw start
                if($v['ddid'] == "" || $v['ddid'] == "0"){
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where Dtid    ='".$v['BID']."' and Sid = '".$v['Sid']."' ",0);
                }else{
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where id    ='".$v['ddid']."'",0);
                }
                //added by quanjw end
                
                $dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
                $pdf->SetZeilenhoehe("6");
                $pdf->SetFont('simsun', '', 11); //设置字体样式
                $pdf->SetAligns("center");
                if( $k == $GLOBALS["MEITAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['cd'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['MaterialCode'], $v['cd'],  $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["SHUINI_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JINSHU_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['strength'], $v['xincengWeight'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   //added by shizg started 2016/09/21 
                }elseif( $k == $GLOBALS["TKS_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['tks_fe'], $v['tks_si'], $v['tks_al'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   
                   //added by shizg ended 2016/09/21 
                }else{
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }
            }
            $pdf->Ln();
        } 
        //Updated by quanjw for meijiao start 2015/7/13
        
        $pdf->SetWidths(array(189)); // 设置每列的宽度
        $pdf->Row(array('合计人民币金额（大写）：'. $this->cny($contact['TotalMoney']) ) ); // 设置列名
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        $pdf->Write(10, '注：1、单价为：');
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['fkxs'] == "1" || $contact['fkxs'] == "2" || $contact['fkxs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     现款含税出厂（或出库）价格 ');
        
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['fkxss'] == "4" || $contact['fkxss'] == "5")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     承兑含税出厂（或出库）价格。');


        $pdf->Ln();
        $pdf->SetX(17);
        $pdf->Write(9, '2、具体交易数量以实际交付为准，以单价乘以实际交付数量为交易金额。');
        $pdf->Ln();
        $pdf->SetX(17);
        $pdf->Write(9, '3、如果没有填写具体交易商品明细，则见附件。');

        // 使用类方法addline替代重复的函数定义

        $pdf->Ln();
        $pdf->SetX(10);

        addline('第二条 产品质量要求：货物质量标准按 ',   "   ".$contact['zlbz']."   "  , ' 标准执行。', $pdf);

        $pdf->Ln();

        addline('第三条 包装标准、包装物的供应与回收：', "   ".$contact['bzbz']."   ", '。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第四条 交货日期和交货地点及收货单位');

        $pdf->Ln();
        addline('（一）交货地点：', "   ".$contact['PickUpAddress']."   ", '。', $pdf);

        $pdf->Ln();
        addline('（二）收（提）货单位：', "   ".$buycomp['ComName']."   ", '。', $pdf);
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第五条 运输方式、费用和交付条件');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（一）运输方式：');
        
        $pdf->SetFont('simsun', '', 12);
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     自提 ');
        
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     代运（');


        $pdf->SetFont('simsun', 'U', 12);
        $x5 = $pdf->GetX();
        $y5 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x5, $y5);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x5, $y5);
        } 
        $pdf->Write(10, '       汽车运输、');
        
        $x6 = $pdf->GetX();
        $y6 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x6, $y6);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x6, $y6);
        } 
        $pdf->Write(10, '       火车运输、');
        
        $x7 = $pdf->GetX();
        $y7 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x7, $y7);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x7, $y7);
        } 
        $pdf->Write(10, '       水运）。');


        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（二）');
        
        $pdf->SetFont('simsun', '', 12);
        $x9 = $pdf->GetX();
        $y9 = $pdf->GetY();
        if (in_array("1",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x9, $y9);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x9, $y9);
        } 
        $pdf->Write(10, '    运杂费、');

        $pdf->SetFont('simsun', '', 12);
        $x10 = $pdf->GetX();
        $y10 = $pdf->GetY();
        if (in_array("2",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x10, $y10);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x10, $y10);
        } 
        $pdf->Write(10, '    出库费、');
        
        $x11 = $pdf->GetX();
        $y11 = $pdf->GetY();
        if (in_array("3",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x11, $y11);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x11, $y11);
        } 
        $pdf->Write(10, '    装卸吊装费、');
        
        $x12 = $pdf->GetX();
        $y12 = $pdf->GetY();
        if (in_array("4",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x12, $y12);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x12, $y12);
        } 
        $pdf->Write(10, '    保管费、');

        $x13 = $pdf->GetX();
        $y13 = $pdf->GetY();
        if (in_array("5",$contact['yzf_Shipper']))
        {
            $pdf->Image("images/gou1.jpg", $x13, $y13);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x13, $y13);
        } 
        $pdf->Write(10, '    保险费等保险费等与物流有关的费用由甲方承担，开具发票为两票制；如果  ');

        $pdf->SetFont('simsun', '', 12);
        $x14 = $pdf->GetX();
        $y14 = $pdf->GetY();
        if (in_array("1",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x14, $y14);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x14, $y14);
        } 
        $pdf->Write(10, '    运杂费、');

        $pdf->SetFont('simsun', '', 12);
        $x15 = $pdf->GetX();
        $y15 = $pdf->GetY();
        if (in_array("2",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x15, $y15);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x15, $y15);
        } 
        $pdf->Write(10, '    出库费、');
        
        $x16 = $pdf->GetX();
        $y16 = $pdf->GetY();
        if (in_array("3",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x16, $y16);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x16, $y16);
        } 
        $pdf->Write(10, '    装卸吊装费、');
        
        $x17 = $pdf->GetX();
        $y17 = $pdf->GetY();
        if (in_array("4",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x17, $y17);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x17, $y17);
        } 
        $pdf->Write(10, '    保管费、');

        $x18 = $pdf->GetX();
        $y18 = $pdf->GetY();
        if (in_array("5",$contact['yzf_Consignee']))
        {
            $pdf->Image("images/gou1.jpg", $x18, $y18);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x18, $y18);
        } 
        $pdf->Write(10, '    保险费等与物流有关的费用由乙方承担，开具发票为一票制。');
 
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun','',12); //设置字体样式
        addline('（三）货物交付条件：乙方发出《提货单》前，甲方应将指定收货人的姓名、身份证号码，车船号等信息书面通知乙方。如果甲方在提货单记载的交货日期内提取货物，交付货物时乙方应提供磅码单、货物出厂质量证明书等货物随行相关证明一式',"   ".$contact['xgzs']."   ",'份。' , $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（四）货物交付给甲方之前，货物的损毁，灭失和失窃的风险由乙方承担。货物交付给甲方或甲方指定的提货人在货物验收单据上签字，双方签收确认后即视为货物转移。');
   
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第六条 货物验收');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）数量确认：甲方提取货物时按：以（ ');

        $x3 = $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['hwsl'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
        } 
        $pdf->Write(10, '    过磅  ');

        $x4 = $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['hwsl'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '    理计）确认接收货物数量。由甲方或甲方指定的提货人在货物验收单据上签字或盖章确认。过磅磅差在±3‰（含）之内的，结算数量以乙方磅单数量为准，复磅费用由责任方承担；磅差在±3‰之外的，经双方协商做复磅处理，复磅磅单与乙方磅单磅差仍大于±3‰的，其结算数量以复磅磅单为准，复磅费用由乙方承担。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）质量检验：甲方按约定的质量标准进行验收，在货物交付后15日内向乙方提出书面异议，由双方协商解决。甲方验收后，并不免除乙方对交付货物的质量保证责任。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'（三）发票开具：乙方按甲方结算金额开具等额有效的增值税专用发票，发票记载的货物品名、规格型号等信息必须与本合同约定一致，否则甲方可要求乙方重开，因此产生的费用由乙方承担。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第七条 结算方式及付款期限');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式

        addline('（一）结算方式： 以', "   ".$GLOBALS['PAYTYPE'][$contact['PayType']]."   ", '方式交易。先货后款是指：收到货物及发票后再支付货款。', $pdf);
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）付款方式： ');
        $x = $pdf->GetX();
        $y = $pdf->GetY();
        if ($contact['fkxs'] == "1" || $contact['fkxs'] == "2" || $contact['fkxs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x, $y);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x, $y);
        } 
        $pdf->Write(10, '    银行汇款、 ');

        $x2 = $pdf->GetX();
        $y2 = $pdf->GetY();

        if ($contact['fkxs'] == "4")
        {
            $pdf->Image("images/gou1.jpg", $x2, $y2);
            addline('    ', "  ".$contact['cdhp']."  ", '天银行承兑汇票、',$pdf);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x2, $y2);
            addline('    ', "  ", '天银行承兑汇票、',$pdf);
        } 
        
        $x3= $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['fkxs'] == "5")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
            addline('    ', "  ".$contact['cdhp']."  ", '天商业承兑汇票、',$pdf);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
            addline('    ', "  ", '天商业承兑汇票、',$pdf);
        }

        $x4= $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['fkxs'] == "6")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '    国内信用证、');

        $x5 = $pdf->GetX();
        $y5 = $pdf->GetY();
        if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8" || $contact['fkxs'] == "9"  || $contact['fkxs'] == "10")
        {
            $pdf->Image("images/gou1.jpg", $x5, $y5);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x5, $y5);
        }
        if ($contact['fkxs'] == "10")
        {
        $tempfkxs=$contact['cdhp']."天".$GLOBALS['ZFKXS'][$contact['fkxs']];
        addline('    其他', "      ".$tempfkxs."      ", '，',$pdf);
        }
        else if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8")
        {
        $tempfkxs=$GLOBALS['ZFKXS'][$contact['fkxs']];
        $tempfkxs=$tempfkxs."（".$contact['dwname']."）";
        addline('    其他', "      ".$tempfkxs."      ", '，',$pdf);
        }
        else{
        addline('    其他', "      ".$contact['dwname']."      ", '，',$pdf);  
        }

        if($contact['txcd']=='1'){
            $txcd = "乙方";
        }else{
            $txcd = "甲方";
        }

        addline('由此所产生的贴息费用等相关费用由', "   ".$txcd."   ", '承担。', $pdf);
        

        $pdf->Ln();
        addline('（三）履约保证金：甲乙双方为了保证及时支付货款或保证货物及时安全交付，甲方和乙方同意按货款', "   ".$contact['ycfkwyj']."   ", '‰支付履约保证金，', $pdf);
        addline('由', "  甲方   ", '在1日内支付。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（四）结算价格确认： ');
        $x19 = $pdf->GetX();
        $y19 = $pdf->GetY();
        if ($zy['bjfs']=="1")
        {
            $pdf->Image("images/gou1.jpg", $x19, $y19);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x19, $y19);
        } 
        $pdf->Write(10, '    锁定价格，则本合同签订的货物单价作为结算价格，不可更改。');

        $x20 = $pdf->GetX();
        $y20 = $pdf->GetY();
        if ($zy['bjfs']== "3")
        {
            $pdf->Image("images/gou1.jpg", $x20, $y20);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x20, $y20);
        } 
        $pdf->Write(10, '    协议机制，则本合同签订的货物单价允许进行浮动修改，结算价格由双方最终在订货环节中进行确定。');



        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第八条 违约责任');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）乙方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、乙方交付的产品：品种、型号、规格和质量等不符合本合同规定的，如果甲方同意使用，应当另行协商；如果甲方不能使用或不同意使用的，乙方应无条件换货、退货并承担相应费用且赔偿甲方由此受到的经济损失。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2、乙方无正当理由单方解除合同的，应立即赔偿由此给甲方造成的经济损失。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '3、乙方须严格按照合同约定时间供货，每逾期一天，乙方须向甲方支付相应货款%的逾期违约金。');


        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（二）甲方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、甲方无正当理由单方解除合同的，应赔偿由此给乙方造成的经济损失。', $pdf);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '第九条 争议的解决方式');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '合同争议的解决方式：本合同项下发生的争议，由甲乙双方协商解决或申请调解解决；协商或调解不成，按下列方式解决：');

        $x19 = $pdf->GetX();
        $y19 = $pdf->GetY();
        if ($contact['DisputeSettlement']=="1")
        {
            $pdf->Image("images/gou1.jpg", $x19, $y19);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x19, $y19);
        } 
        addline('    提交'," ".$contact['DisputeSettlement_city']." ", '仲裁委员会仲裁。',$pdf);
        //$pdf->Write(10, '    提交'.$contact['DisputeSettlement_city'].'仲裁委员会仲裁。');

        $x20 = $pdf->GetX();
        $y20 = $pdf->GetY();
        if ($contact['DisputeSettlement']== "2")
        {
            $pdf->Image("images/gou1.jpg", $x20, $y20);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x20, $y20);
        } 
        $pdf->Write(10, '    向合同签订地人民法院诉讼。');        
        $pdf->Ln();

        $pdf->Write(10, '第十条 其他约定事项：如果任意一方提出合同条款及事项进行变更，则应提前 3 天书面通知对方，征得同意后，可签订书面协议，否则应承担违约责任。');
        $pdf->Ln();

        $pdf->Write(10, '第十一条 本合同自双方签字盖章之日生效，共一式陆份，买卖双方各执叁份，具有同等法律效力。本合同在双方权利义务履行完毕后自行失效。');
        $pdf->Ln();
        //added by hzp started 2015/08/17
        if($contact['fjtk'] !=""){
        $pdf->Write(10, '第十二条 附加条款： '.$contact['fjtk'].'');
        $pdf->Ln();
        }
        //added by hzp ended 2015/08/17
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '甲方（盖章）：');
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],0,18));
        $pdf->SetX(115);
        $pdf->Write(10, '乙方（盖章）：');
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],0,16));
        $pdf->Ln();

    if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
        $pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
        $pdf->Ln();
    }
        $pdf->SetX(10);
        $pdf->Write(10, '法定代表人：');
        $pdf->Write(10, $salecomp['Frdb']);

        $pdf->SetX(115);
        $pdf->Write(10, '法定代表人：');
        $pdf->Write(10, $buycomp['Frdb']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $this->c_substr($salecomp['Address'],0,15));

        $pdf->SetX(115);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $this->c_substr($buycomp['Address'],0,13));
        $pdf->Ln();


    if(strlen($salecomp['Address']) > 30 || strlen($buycomp['Address']) > 26){
        $pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['Address'],15,strlen($salecomp['Address'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['Address'],13,strlen($buycomp['Address'])));
        $pdf->Ln();
    }


        $pdf->SetX(10);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $salecomp['ContactTel']);

        $pdf->SetX(115);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $salecomp['ContactFax']);

        $pdf->SetX(115);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($salecomp['BankType'],0,12));

        $pdf->SetX(115);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($buycomp['BankType'],0,11));
        $pdf->Ln();

    if(strlen($salecomp['BankType']) > 24 || strlen($buycomp['BankType']) > 22){
        $pdf->SetX(35);
        
        $pdf->Write(10, $this->c_substr($salecomp['BankType'],12,strlen($salecomp['BankType'])));

        $pdf->SetX(140);
        
        $pdf->Write(10, $this->c_substr($buycomp['BankType'],11,strlen($buycomp['BankType'])));
        $pdf->Ln();
    }

        $pdf->SetX(10);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $salebank['MBR_SPE_ACCT_NO']);

        $pdf->SetX(115);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $buybank['MBR_SPE_ACCT_NO']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $salecomp['TaxNo']);

        $pdf->SetX(115);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $buycomp['TaxNo']);
        $pdf->Ln();
        // $pdf->Uni_putpages();
        // 二维码
        $b = str_replace(' ', '', $contact['TwoDimCode']); 
        // $pdf->Write(10,$c);
        $c = WEB_SITE."/member.php?view=yzpdf&hid=" . $hth . "&dimcode=" . $b."&dtid=".$contact['BID'];

        QRcode::png($c, "images/ewm/" . $contact['ContractNo'] . ".png");

        $sc = urlencode($c);

        $xer = $pdf->GetX();
        $yer = $pdf->GetY();

        $pdf->Image("images/ewm/" . $contact['ContractNo'] . ".png", $xer, $yer);

        $pdf->SetXY($xer+8,$yer+49);
        $pdf->SetFont('simsun', '', 8);
        $pdf->Write($xer, '扫描二维码可辨别此合同真伪');

        $pdf->SetFont('simsun', '', 12);
        if($params['fromapp']=='1'){
            echo base64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }else{
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        }
        
    }
    //added by hezp ended 2017/10/16

    //added by hezp started 2017/01/10
    public function index_isec($params)
    {
        $hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $hth . "'");
        //$zydetail = $this->zy_format($zydetail);
        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
        //updated by hzp ,竞价也当协议价20150806 started
        if($zy['bjfs']=="2" || $zy['bjfs'] == "3"){
        $zy['bjfs'] = "3";
        }
        //updated by hzp ,竞价也当协议价20150806 end
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($buycomp['signCompanyname'] !=""){
            $buycomp['ComName'] = $buycomp['signCompanyname'];
        }else{
            $buycomp['ComName'] = $buycomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        //added by hzp for yunqian started 2015/08/05
        if($salecomp['signCompanyname'] !=""){
            $salecomp['ComName'] = $salecomp['signCompanyname'];
        }else{
            $salecomp['ComName'] = $salecomp['ComName'];
        }
        //added by hzp for yunqian ended 2015/08/05
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->AddGBFont('kaiti', '华文楷体');

        $pdf->Open();
        $pdf->AddPage();
        
        // 使用类方法addline替代重复的函数定义
        // XK start deleted for login 2014/11/28
        //$pdf->SetAutoPageBreak(true);
        // XK end deleted for login 2014/11/28
        
        
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        
        $pdf->SetX(10);
        $pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 130, 10,70,8);
        // update by libing end for barcode in 2015/08/07

        $pdf->Line(10, 20, 200, 20);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(80);
        $pdf->SetFont('simsun', 'B', 14);
        $pdf->Write(9, '货物购销合同');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10, '供方：');
        $pdf->SetFont('simhei', '', 12);
        $pdf->Write(10, $salecomp['ComName'].'（下称甲方）');
        
        $pdf->SetX(115);
        $pdf->SetFont('simsun', '', 12);
        $pdf->Write(10,'合同编号：');
        $pdf->Write(10, $contact['ContractNo']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '   ');
        

        $pdf->SetX(115);
        $pdf->Write(10,'签订地点：');
        $pdf->Write(10, $contact['qdaddress']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '需方：');
        $pdf->SetFont('simsun', '', 12);
        $pdf->Write(10, $buycomp['ComName'].'（下称乙方）');

        $pdf->SetX(115);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'签订时间：');
        //$pdf->Write(10, $contact['qdtime']);
        addline('', date("Y",strtotime($contact['qdtime'])), '年', $pdf);
        addline('', date("m",strtotime($contact['qdtime'])), '月', $pdf);
        addline('', date("d",strtotime($contact['qdtime'])), '日', $pdf);
        $pdf->Ln();

        $pdf->SetX(20);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '甲乙双方本着互惠互利、共同发展的原则，在平等自愿的基础上，经友好协商，现就乙方在国内各地区项目建筑钢材、型材、管材、板材等钢材的销售与采购事宜签订以下合同：');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第一条  货物名称、牌号商标、生产厂家、型号规格、数量、价格及金额：');

        //Updated by quanjw for meijiao start 2015/7/13
        $pdf->Ln();
        $pdf->SetX(10);
        //$pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        //$pdf->SetAligns("Center");
        
        
        //资源分类
        $zyll = $this->groupResourse($zydetail);
        
        foreach($zyll as $k => $res)
        {
            //输出分类资源标题
            //$pdf->SetAligns("C");
            if( $k == $GLOBALS["MEITAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 30, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '挥发分', '灰分','全硫分', '产地', "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)",'交货期')); // 设置列名    
            }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 30, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '灰分', '全硫分','CSR', '产地', "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)",'交货期')); // 设置列名    
            }elseif( $k == $GLOBALS["SHUINI_VID"]){
                $pdf->SetWidths(array(36, 36, 22, 22, 30, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称','规格', '产地', "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)",'交货期')); // 设置列名    
            }elseif( $k == $GLOBALS["JINSHU_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 30, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', '规格', '强度', '锌层重量', '产地', "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)",'交货期')); // 设置列名    
            }elseif( $k == $GLOBALS["TKS_VID"]){
                $pdf->SetWidths(array(18, 18, 18,18, 22, 22, 30, 22,22)); // 设置每列的宽度
                $pdf->Row(array('产品名称', 'Fe%', 'SiO2%', 'AL2O3%', '产地', "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)",'交货期')); // 设置列名     
            }else{
                $pdf->SetWidths(array(24, 24, 24, 22, 22, 30, 22,22)); // 设置每列的宽度
                
                $pdf->Row(array("产品名称", "材质", "规格", "产地", "数量\n(吨)", "含税单价\n(元/吨)", "金额\n(元)","交货期")); // 设置列名    
            }

            $pdf->SetFont('simsun', '', 11); //设置字体样式
            $tmoney = 0;
            $tweight = 0;
            foreach($res as $v){
                //输出资源详细信息
                //added by quanjw start
                if($v['ddid'] == "" || $v['ddid'] == "0"){
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where Dtid    ='".$v['BID']."' and Sid = '".$v['Sid']."' ",0);
                }else{
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where id    ='".$v['ddid']."'",0);
                }
                //added by quanjw end
                
                $dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
                $tmoney +=$dtjgMoney;
                $tweight +=$v['BuyQuantity'];
                $pdf->SetZeilenhoehe("6");
                $v['VarietyName'] = html_entity_decode($v['VarietyName']);
                $v['MaterialCode'] = html_entity_decode($v['MaterialCode']);
                $v['SpecCode'] = html_entity_decode($v['SpecCode']);
                $v['OriginCode'] = html_entity_decode($v['OriginCode']);
                //$v['tks_fe'] = html_entity_decode($v['tks_fe']);
                //$v['tks_si'] = html_entity_decode($v['tks_si']);
                //$v['tks_al'] = html_entity_decode($v['tks_al']);
                $v['strength'] = html_entity_decode($v['strength']);
                $v['xincengWeight'] = html_entity_decode($v['xincengWeight']);
                //$pdf->SetAligns("Center");
                if( $k == $GLOBALS["MEITAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['cd'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['MaterialCode'], $v['cd'],  $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["SHUINI_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }elseif( $k == $GLOBALS["JINSHU_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['SpecCode'], $v['strength'], $v['xincengWeight'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   //added by shizg started 2016/09/21 
                }elseif( $k == $GLOBALS["TKS_VID"]){
                    $pdf->Row(array($v['VarietyName'], $v['tks_fe'], $v['tks_si'], $v['tks_al'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名   
                   //added by shizg ended 2016/09/21 
                }else{
                    $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['OriginCode'], $v['BuyQuantity'], $v['SalesPrice'], $dtjgMoney,$pickdate)); // 设置列名    
                }
            }
            

            if( $k == $GLOBALS["MEITAN_VID"] || $k == $GLOBALS["JIAOTAN_VID"] || $k == $GLOBALS["JINSHU_VID"] || $k == $GLOBALS["TKS_VID"] ){
                $pdf->SetFont('simsun', 'B', 11); //设置字体样式
                $pdf->Row(array('合计', '','','','', $tweight, '', $tmoney,''));
            }else if( $k == $GLOBALS["SHUINI_VID"]){
                $pdf->SetFont('simsun', 'B', 11); //设置字体样式
                $pdf->Row(array('合计', '','', $tweight, '', $tmoney,''));
            }else{
                $pdf->SetFont('simsun', 'B', 11); //设置字体样式
                $pdf->Row(array('合计', '','','', $tweight, '', $tmoney,''));
            }

            $pdf->Ln();
           
        } 
        //Updated by quanjw for meijiao start 2015/7/13
        
        $pdf->SetWidths(array(190)); // 设置每列的宽度
        $pdf->SetWidths(array(94, 96)); // 设置每列的宽度
        $pdf->Row(array('合计人民币金额（大写）', $this->cny($contact['TotalMoney']) ) ); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        if($contact['fjtk']==''){
            $contact['fjtk'] = "无";
        }
        $pdf->Write(9, '备注：'.$contact['fjtk']);
        

        

        $pdf->SetMargins(20, 10, 15);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第二条  货物的计量单位、方法及合理损耗：');
        $pdf->Ln();

        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '货物为（ ');

        $x3 = $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['hwsl'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
        } 
        $pdf->Write(10, '    过磅  ');

        $x4 = $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['hwsl'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '    理计）交货。以指定签收人签收的重量为准。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第三条  货物的交货单位、地点和时间：');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1. 交货单位：'.$buycomp['ComName']);

        $pdf->Ln();
        $pdf->Write(10, '2. 交货地点：'.$contact['PickUpAddress']);

        $pdf->Ln();
        $pdf->Write(10, '3. 收货人：'.$contact['ConsigneeMan']."   ".$contact['ConsigneeMobile']);
        
        
        $pdf->Ln();
        $pdf->Write(10, '4. 交货时间：甲方应当按照乙方进货计划(品名、规格型号、计量单位、数量、交货时间、送货地点等)供货。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第四条  运输方式');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'运输方式：( ');
        
        $pdf->SetFont('simsun', '', 12);
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     自提 ');
        
        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        if ($contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '     代办)');


        if ($contact['Delivery'] == "1"){
            $pdf->Write(10,'  乙方自提。');
        }else{
        
            $pdf->Write(10,'  甲方负责将货物送到乙方指定地点，随车附带质保书。');
        
            addline('运费承担方：', "   ".$GLOBALS['HTSF'][$contact['ysfy']]."   ", '。', $pdf);
        }


        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第五条  货物的验收、提出异议期限及异议解决的方式：');
   
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1.甲方交货后，乙方向甲方出具物资签收单。乙方出具物资签收单后，物资所有权转移给乙方。');

        $pdf->Ln();
        addline('2.乙方在验收中如发现货物的品种、型号、规格和质量等不合规定或约定，应在妥为保管货物的同时，自收到货物', "   5  ", '日内向甲方提出书面异议；', $pdf);
        addline('乙方未及时提出异议或者自收到货物之日起', "   5   ", '日内未通知甲方的，视为货物合乎规定。', $pdf);

       

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第六条  结算条款：');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1.付款方式： ');
        $x2 = $pdf->GetX();
        $y2 = $pdf->GetY();
        if ($contact['PayType']=="1")
        {
            $pdf->Image("images/gou1.jpg", $x2, $y2);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x2, $y2);
        } 
        $pdf->Write(10, '       先款后货、');

        $x = $pdf->GetX();
        $y = $pdf->GetY();
        if ($contact['PayType'] == "4")
        {
            $pdf->Image("images/gou1.jpg", $x, $y);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x, $y);
        } 
        $pdf->Write(10, '       先货后款。');
        if($contact['fkqx']=='0'){
            $contact['fkqx'] = "当日";
        }else{
            $contact['fkqx'] = $contact['fkqx']."日";
        }
        if($contact['jhqx']=='0'){
            $contact['jhqx'] = "当日";
        }else{
            $contact['jhqx'] = $contact['jhqx']."日";
        }
        if($contact['PayType'] == "4"){
            addline('合同签订当日甲方按运输方式约定交货，乙方验收合格后', "   ".$contact['fkqx']."   ", '内付全部货款给甲方。',$pdf);
        }else{
            addline('合同签订', "   ".$contact['fkqx']."   ", '乙方以（',$pdf);
            //addline('以', "   ".$contact['fkqx']."   ", '的方式一次性支付全部货款给甲方，',$pdf);

            $x = $pdf->GetX();
            $y = $pdf->GetY();
            if ($contact['fkxs'] == "1" || $contact['fkxs'] == "2" || $contact['fkxs'] == "3")
            {
                $pdf->Image("images/gou1.jpg", $x, $y);
            } 
            else
            {
                $pdf->Image("images/gou2.jpg", $x, $y);
            } 
            $pdf->Write(10, '       银行汇款、 ');

            $x2 = $pdf->GetX();
            $y2 = $pdf->GetY();

            if ($contact['fkxs'] == "4")
            {
                $pdf->Image("images/gou1.jpg", $x2, $y2);
                addline('   ', "  ".$contact['cdhp']."  ", '天银行承兑汇票、',$pdf);
            } 
            else
            {
                $pdf->Image("images/gou2.jpg", $x2, $y2);
                addline('   ', "  ", '天银行承兑汇票、',$pdf);
            } 
            
            $x3= $pdf->GetX();
            $y3 = $pdf->GetY();
            if ($contact['fkxs'] == "5")
            {
                $pdf->Image("images/gou1.jpg", $x3, $y3);
                addline('   ', "  ".$contact['cdhp']."  ", '天商业承兑汇票、',$pdf);
            } 
            else
            {
                $pdf->Image("images/gou2.jpg", $x3, $y3);
                addline('   ', "  ", '天商业承兑汇票、',$pdf);
            }

            $x4= $pdf->GetX();
            $y4 = $pdf->GetY();
            if ($contact['fkxs'] == "6")
            {
                $pdf->Image("images/gou1.jpg", $x4, $y4);
            } 
            else
            {
                $pdf->Image("images/gou2.jpg", $x4, $y4);
            } 
            $pdf->Write(10, '   国内信用证、');

            $x5 = $pdf->GetX();
            $y5 = $pdf->GetY();
            if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8" || $contact['fkxs'] == "9" || $contact['fkxs'] == "10")
            {
                $pdf->Image("images/gou1.jpg", $x5, $y5);
            } 
            else
            {
                $pdf->Image("images/gou2.jpg", $x5, $y5);
            }
            if ($contact['fkxs'] == "10")
            {
            $tempfkxs=$contact['cdhp']."天".$GLOBALS['ZFKXS'][$contact['fkxs']];
            addline('   其他', "      ".$tempfkxs."      ", '） ',$pdf);
            }
            else if ($contact['fkxs'] == "7" || $contact['fkxs'] == "8")
            {
            $tempfkxs=$GLOBALS['ZFKXS'][$contact['fkxs']];
            $tempfkxs=$tempfkxs."（".$contact['dwname']."）";
            addline('   其他', "      ".$tempfkxs."      ", '） ',$pdf);
            }
            else{
            addline('   其他：', "      ".$contact['dwname']."      ", '） ',$pdf);  
            }

            addline('的方式一次性支付全部货款的', "   ".$contact['lybzj']."   ", '%',$pdf);
            addline('给甲方，甲方在收到', "   ".$contact['lybzj']."   ", '%货款',$pdf);
            addline('后的', "   ".$contact['jhqx']."内   ", '，按照要求将货物送至指定的交货地点。',$pdf);
            
        }
        
        $pdf->Ln();
        $pdf->Write(10, '2.结算价格和数量：双方根据订货价格及实际交货数量进行货款结算。本合同币种为人民币。以上金额包括货物、出库费、运输费、卸车费，一票结算');

        

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第七条  双方承诺： ');
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1.乙方需按时付款。');

        $pdf->Ln();
        $pdf->Write(10, '2.若遇不可抗或者排产等因素不能按期交货的，甲方须提前通知乙方，以便乙方另行组织资源。');



        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第八条  不可抗力:');

        
        $pdf->Ln();
        
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '本合同中，不可抗力是指不能预见、不能避免并不能克服的客观情况。包括：战争、自然灾害等或由于人力不可抗拒不能履行合同的情况。任何一方由于不可抗力原因不能履行合同时，应在不可抗力事件结束3日内向对方通报，以减轻可能给对方造成的损失，在取得有关机构的不可抗力证明后，允许延期履行、部分履行或者不履行合同，并根据情况可部分或全部免于承担违约责任。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第九条  解决合同纠纷的方式：');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        //$pdf->Write(10, '凡因本合同引起的或与本合同有关的任何争议，如双方不能通过友好协商解决，提起诉讼由合同签订地法院管辖。');
        
        if($contact['DisputeSettlement']=="1"){
            addline('凡因本合同引起的或与本合同有关的任何争议，如双方不能通过友好协商解决，提交'," ".$contact['DisputeSettlement_city']." ", '仲裁委员会仲裁。',$pdf);
            //$pdf->Write(10, '合同争议的解决方式：本合同项下发生的争议，由买卖双方协商解决或申请调解解决；协商或调解不成，按下列第（一）种方式解决：');
        }
        if($contact['DisputeSettlement']=="2"){
            $pdf->Write(10, '凡因本合同引起的或与本合同有关的任何争议，如双方不能通过友好协商解决，向合同签订地法院提起诉讼。');
        }
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第十条  合同生效及期限：');
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '本合同自甲、乙双方签字盖章生效，账款结清后自动终止。');
        $pdf->Ln();
        
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第十一条  本协议交易在“'.WEBNAME.'（'.WEB_SITE.'）”上进行。');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '第十二条  其它约定事项：');
        $pdf->Ln();

        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1. 按本合同规定应付的违约金、赔偿金、保管保养费和各种经济损失，应当在明确责任5日内，按银行规定的结算办法付清，否则按逾期付款处理。');
        $pdf->Ln();

        $pdf->Write(10, '2. 合同原件的传真件及其复印件（与原件内容一致），具有与原件同等的法律效力。');
        $pdf->Ln();

        $pdf->Write(10, '3.	本合同一式肆份，双方各执贰份。');
        $pdf->Ln();
        
        $pdf->Ln();

        $pdf->SetX(40);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '甲方');
        $pdf->SetX(145);
        $pdf->Write(10, '乙方');
        $pdf->Ln();

        
        $pdf->SetX(10);
        $y = $pdf->gety();
        $pdf->Write(10, '单位名称：');
        //$pdf->Write(10, $salecomp['ComName']);
        $pdf->MultiCell(70,10,$salecomp['ComName'],0,'');
        //$pdf->SetX(115);
        $pdf->setxy(115,$y);
        $pdf->Write(10, '单位名称：');
       // $pdf->Write(10, $buycomp['ComName']);
        $pdf->MultiCell(60,10,$buycomp['ComName'],0,'');
        $pdf->Ln();

        /*$pdf->SetX(10);
        $pdf->Write(10, '单位名称：');
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],0,40));
        $pdf->SetX(115);
        $pdf->Write(10, '单位名称：');
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],0,40));
        $pdf->Ln();

        if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
            $pdf->SetX(25);
            
            $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

            $pdf->SetX(130);
            
            $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
            $pdf->Ln();
        }*/

        $pdf->SetX(10);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $y = $pdf->gety();
        $pdf->Write(10, '单位地址：');
        $pdf->MultiCell(70,10,$salecomp['Address'],0,'');
        $pdf->setxy(115,$y);
        $pdf->Write(10, '单位地址：');
        $pdf->MultiCell(60,10,$buycomp['Address'],0,'');
        $pdf->Ln();
        /*$pdf->SetX(10);
        $pdf->Write(10, '单位地址：');
        $pdf->Write(10, $this->c_substr($salecomp['Address'],0,15));

        $pdf->SetX(115);
        $pdf->Write(10, '单位地址：');
        $pdf->Write(10, $this->c_substr($buycomp['Address'],0,13));
        $pdf->Ln();
        if(strlen($salecomp['Address']) > 30 || strlen($buycomp['Address']) > 26){
            $pdf->SetX(25);
            
            $pdf->Write(10, $this->c_substr($salecomp['Address'],15,strlen($salecomp['Address'])));

            $pdf->SetX(130);
            
            $pdf->Write(10, $this->c_substr($buycomp['Address'],13,strlen($buycomp['Address'])));
            $pdf->Ln();
        }*/

        $pdf->SetX(10);
        $pdf->Write(10, '法定代表人：');
        $pdf->Write(10, $salecomp['Frdb']);

        $pdf->SetX(115);
        $pdf->Write(10, '法定代表人：');
        $pdf->Write(10, $buycomp['Frdb']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '委托代理人：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '委托代理人：');
        $pdf->Write(10, '');
        $pdf->Ln();


        
        $pdf->SetX(10);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $salecomp['ContactTel']);

        $pdf->SetX(115);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $salecomp['ContactFax']);

        $pdf->SetX(115);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $buycomp['ContactFax']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '邮政编码：');
        $pdf->Write(10, $salecomp['postcode']);

        $pdf->SetX(115);
        $pdf->Write(10, '邮政编码：');
        $pdf->Write(10, $buycomp['postcode']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $salecomp['TaxNo']);

        $pdf->SetX(115);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $buycomp['TaxNo']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($salecomp['BankType'],0,12));

        $pdf->SetX(115);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10,  $this->c_substr($buycomp['BankType'],0,11));
        $pdf->Ln();

        if(strlen($salecomp['BankType']) > 24 || strlen($buycomp['BankType']) > 22){
            $pdf->SetX(35);
            
            $pdf->Write(10, $this->c_substr($salecomp['BankType'],12,strlen($salecomp['BankType'])));

            $pdf->SetX(140);
            
            $pdf->Write(10, $this->c_substr($buycomp['BankType'],11,strlen($buycomp['BankType'])));
            $pdf->Ln();
        }

        $pdf->SetX(10);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $salebank['MBR_SPE_ACCT_NO']);

        $pdf->SetX(115);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $buybank['MBR_SPE_ACCT_NO']);
        $pdf->Ln();

        
        // $pdf->Uni_putpages();
        // 二维码
        $b = str_replace(' ', '', $contact['TwoDimCode']); 
        // $pdf->Write(10,$c);
        $c = WEB_SITE."/member.php?view=yzpdf&hid=" . $hth . "&dimcode=" . $b."&dtid=".$contact['BID'];

        QRcode::png($c, "images/ewm/" . $contact['ContractNo'] . ".png");

        $sc = urlencode($c);

        $xer = $pdf->GetX();
        $yer = $pdf->GetY();

        $pdf->Image("images/ewm/" . $contact['ContractNo'] . ".png", $xer, $yer);

        $pdf->SetXY($xer+8,$yer+49);
        $pdf->SetFont('simsun', '', 8);
        $pdf->Write($xer, '扫描二维码可辨别此合同真伪');

        $pdf->SetFont('simsun', '', 12);
        if($params['fromapp']=='1'){
            echo base64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }else{
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        }
        
    }
    //added by hezp ended 2017/01/10
    
    function c_substr($string, $from, $length = null)
    {
            preg_match_all('/[\x80-\xff]?./', $string, $match);
            if(is_null($length)){
                    $result = implode('', array_slice($match[0], $from));
            }else{
                    $result = implode('', array_slice($match[0], $from, $length));
            }
            return $result;
    }


    public function index2($params)
    {
        $cz = $this->_dao->AQuery("SELECT MaterialCode,MaterialName  FROM sm_base_material Where 1=1 ORDER BY ID");
        $gg = $this->_dao->AQuery("SELECT SpecCode,SpecName  FROM sm_base_specification ");
        $gcs = $this->_dao->AQuery("SELECT OriginCode, OriginNameShort FROM sm_base_placeorigin");

        $hth = $params['hth'];
        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");

        $buy = $this->_dao->getRow("select * from sm_exc_buy where ID = '" . $contact['Bid'] . "'");

        $zy = $this->_dao->getRow("select * from sm_exc_sales where ID = '" . $buy['Sid'] . "' "); 
        // 买方公司名称
		//updated by hzp ,竞价也当协议价20150806 started
	if($zy['bjfs']=="2" || $zy['bjfs'] == "3"){
		$zy['bjfs'] = "3";
	}
	//updated by hzp ,竞价也当协议价20150806 end
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
		//added by hzp for yunqian started 2015/08/05
		if($buycomp['signCompanyname'] !=""){
			$buycomp['ComName'] = $buycomp['signCompanyname'];
		}else{
			$buycomp['ComName'] = $buycomp['ComName'];
		}
		//added by hzp for yunqian ended 2015/08/05
		//added by hzp for yunqian started 2015/08/05
		if($salecomp['signCompanyname'] !=""){
			$salecomp['ComName'] = $salecomp['signCompanyname'];
		}else{
			$salecomp['ComName'] = $salecomp['ComName'];
		}
		//added by hzp for yunqian ended 2015/08/05
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage(); 
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 14); //设置字体样式
        // $swidth=$pdf->GetStringWidth("中国大宗物资网交易合同");
        $pdf->SetX(80);
        $pdf->Write(10, '中国大宗物资网交易合同');
        $pdf->Ln();

        $pdf->SetAutoPageBreak(true);
        // "合同编号"		预定时间		签订时间		经办人
        $header2 = array('合同编号', $hth, '预定时间', '', '签订时间', '', '经办人', '');
        $width2 = array(20, 52, 20, 20, 20, 20, 20, 20); //设置每列宽度
        $pdf->SetFont('simsun', 'B', 10);
        for($i = 0;$i < count($header2);$i++) // 循环输出表头
        {
            $pdf->Cell($width2[$i], 6, $header2[$i], 1);
        } 
        $pdf->Ln();
        // $header3=array('品名','材质','规格','等级','厂家','仓库','交易地','数量（吨）','价格','金额(元)'); //设置表头
        // $width3=array(30,18,17,17,10,20,20,20,20,20); //设置每列宽度
        // for($i=0;$i<count($header3);$i++){ //循环输出表头 
        // if($i==0){
        // $pdf->SetFont('simsun','B',10);
        // }
        // else
        // {
        // $pdf->SetFont('simsun','',10); //设置字体样式
        // }
        // $pdf->Cell($width3[$i],6,$header3[$i],1);
        // }
        // $pdf->Ln();
        $pdf->SetFont('simsun', '', 10); //设置字体样式
        
        $pdf->SetWidths(array(30, 18, 17, 17, 10, 20, 20, 20, 20, 20)); // 设置每列的宽度
        
        $pdf->Row(array('品名', '材质', '规格', '等级', '厂家', '仓库', '交易地', '数量（吨）', '价格', '金额(元)')); // 设置列名
        
        $TotalMoney = $buy['PriceContention'] * $buy['BuyQuantity'];

        $name = $zy['VarietyName'];
        // $name="dddddddddddddddddddddddddddddddd";
        $pdf->Row(array($name, $cz[$zy['MaterialCode']], $gg[$zy['SpecCode']], '', $gcs[$zy['OriginCode']], '', '', $buy['BuyQuantity'], $buy['PriceContention'], $TotalMoney));
        // $header4=array($pdf->WriteText($name),$cz[$zy['MaterialCode']],$gg[$zy[SpecCode]],'',$gcs[$zy[OriginCode]],'','','','',''); //设置表头
        // $width4=array(30,18,17,17,10,20,20,20,20,20); //设置每列宽度
        // for($i=0;$i<count($header3);$i++){ //循环输出表头 
        // $pdf->Cell($width4[$i],6,$header4[$i],1);
        // }
        // $pdf->WriteText($name);
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 10); //设置字体样式
        
        /* add nicc


$header4=array('线材','','','','','','','','',''); //设置表头
$width4=array(20,18,17,17,20,20,20,20,20,20); //设置每列宽度
for($i=0;$i<count($header4);$i++){ //循环输出表头
	$pdf->Cell($width4[$i],6,$header4[$i],1);
	}
$pdf->Ln();
$pdf->SetFont('simsun','',10); //设置字体样式

$header4=array('合计','','','','','','','','',''); //设置表头
$width4=array(20,18,17,17,20,20,20,20,20,20); //设置每列宽度
for($i=0;$i<count($header4);$i++){ //循环输出表头
	if($i==0){
		$pdf->SetFont('simsun','B',10);
	}
	else
	{
		$pdf->SetFont('simsun','',10); //设置字体样式
	}
	$pdf->Cell($width4[$i],6,$header4[$i],1);
	}
$pdf->Ln();
$pdf->SetFont('simsun','',10); //设置字体样式
*/

        $header4 = array('约束条款', ''); //设置表头
        $width4 = array(20, 172); //设置每列宽度
        for($i = 0;$i < count($header4);$i++) // 循环输出表头
        {
            if ($i == 0)
            {
                $pdf->SetFont('simsun', 'B', 10);
            } 
            else
            {
                $pdf->SetFont('simsun', '', 10); //设置字体样式
            } 
            // $pdf->Write(6,$header4[$i]);
            $pdf->Cell($width4[$i], 90, $header4[$i], 1);
        } 
        // $pdf->Ln();
        $str = "
一、货物质量标准、技术要求、验收标准：执行国家有关标准和行业标准。
二、交割时间：生成有效合同起24小时内。
三、交货地点：买方在约定时间到指定交易仓库提货。
四、交货数量：合同实际交易量原则上为合同约定总量的98%-100%均视为有效交易，最终数量依磅
单为准，超出此有效交易量，双方可以协商处理。
五、付款期限：买方验收合格后3小时内向卖方付款。
六、运输：由买方负责运输事宜，卖方在买方的授权委托下，可代办运输事宜，但应由买方承担运输
风险并负担运杂费、保险费及相关税金等费用，承担因错误指定运输方式和运输地点的相关责任。
七、违约责任：任何一方违约，依照国家有关法律规定和中国大宗物资网的相关交易规则追究其相应
责任。
八、争议解决：双方友好协商解决，如协商不成向卖方所在地人民法院起诉。
九、本合同自买方确认交易后生效。
十、本合同传真件与原件具有同等法律效力。　　 
";
        $pdf->SetLeftMargin(30);
        $pdf->Write(6, $str);

        $pdf->SetFont('simsun', '', 10); //设置字体样式
        $pdf->SetX(10);
        $header4 = array('附加条款', ''); //设置表头
        $width4 = array(20, 172); //设置每列宽度
        for($i = 0;$i < count($header4);$i++) // 循环输出表头
        {
            if ($i == 0)
            {
                $pdf->SetFont('simsun', 'B', 10);
            } 
            else
            {
                $pdf->SetFont('simsun', '', 10); //设置字体样式
            } 
            $pdf->Cell($width4[$i], 6, $header4[$i], 1);
        } 
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetWidths(array(96, 96)); // 设置每列的宽度
        
        $pdf->Row(array('买方', '卖方')); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('单位名称', $buycomp['ComName'], '单位名称', $salecomp['ComName'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('地址', $buycomp['Address'], '地址', $salecomp['Address'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('法人代表', $buycomp['Frdb'], '法人代表', $salecomp['Frdb'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('开户银行', $buybank['MBR_SPE_ACCT_BKID'], '开户银行', $salebank['MBR_SPE_ACCT_BKID'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('银行帐号', $buybank['MBR_SPE_ACCT_NO'], '银行帐号', $salebank['MBR_SPE_ACCT_NO'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('办公电话', $buycomp['ContactTel'], '办公电话', $salecomp['ContactTel'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('传真', $buycomp['ContactFax'], '传真', $salecomp['ContactFax'])); // 设置列名
        
        $pdf->SetX(10);
        $pdf->SetWidths(array(28, 68, 28, 68)); // 设置每列的宽度
        
        $pdf->Row(array('经办人', '', '经办人', '')); // 设置列名
        
        $pdf->Output('g.pdf', 'I');
    } 
	//马钢（慈湖）合同模板
	public function htmodle($params)
    {
        $hth = $params['hth'];
		//$this->setvars();
        include("classfile.php");
        include("phpqrcode.php");

		

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail where TID = '" . $hth . "'");

		//总价格
		$totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail GROUP BY TID HAVING TID = '" . $hth . "'");

		//总数量
		$totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail GROUP BY TID HAVING TID = '" . $hth . "'");

		//品种
		$pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");

        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
		//买方公司联系人
		$buyuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$buycomp['ID']."' and IsMain='1' limit 1 "); 
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

         $pdf = new PDF_Chinese("L","mm", "A4");


        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true);

        $pdf->SetFont('simhei','',18);
		$pdf->SetX(80);
		$pdf->Write(20, '马钢（慈湖）钢材加工配售有限公司销售合同');
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->SetX(20);
		$pdf->Write(10, '签订日期：');
        $pdf->Write(10, $contact['qdtime']);

		$pdf->SetX(105);
        $pdf->Write(10, '签订地点：');
        $pdf->Write(10, $contact['qdaddress']);

		$pdf->SetX(190);
        $pdf->Write(10, '合同号：');
        $pdf->Write(10, $contact['ContractNo']);

        $pdf->Ln();
        $pdf->SetX(18);
        $pdf->SetWidths(array(31, 31, 31, 35, 31, 35, 31, 35)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
        $pdf->Row(array('合同总量', $totalweight, '付款方式', $GLOBALS['PAYTYPE'][$contact['PayType']], '交货地点', '马鞍山-（'.$contact["PickUpCity"].')', '交货日期',$contact['PickUpDate'])); 
        $pdf->SetX(18);
        $pdf->Row(array('合同类别', '', '提货方式', '自提/配送', '技术条件', 'MGCH-7.1-01-V', '企业标准','MGCH-J-002')); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(50,210)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
        $pdf->Row(array('含税总金额（人民币大写）', $this->cny($totalmoney))); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(109,151)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
        $pdf->Row(array('产　品　需　求　清　单', '                                                                           价格组成（含税价）')); 
		
		$pdf->SetX(18);
        $pdf->SetWidths(array(9,22,14,24,15,25,20,18,18,18,18,21,18,20)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
        $pdf->Row(array('项号','品      名','钢  种','规格','重量','单价（不含税）','包装方式','包装费','基价','加工费','锌层','技术加减价','运输费','备注')); 
       
        foreach($zydetail as $v)
        { 
			$pdf->SetX(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
			
            $pdf->Row(array('',$v['VarietyName'],$pzs2[$v['VarietyCode']], $v['SpecCode'],$v['BuyQuantity'], $v['SalesPrice'],$contact['bzbz'],'','','','','','','')); 
            
        } 

		$pdf->SetX(18);
		$pdf->SetWidths(array(20,240)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
        $pdf->Row(array('其他约定', '')); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(12,248)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
		$str =
'1、如无特殊说明，价格单位元/吨，数量单位为吨。                                                    4、价格：价格为不含税价，增值税按国家税法规定另收。
2、检验方式及检验期限：按供方质量说明书验收，计量和                                       5、解决合同纠纷方式：双方协商，协商不成，向供方所在地人民法院起诉。
质量问题需在10天内向供方提出书面异议，并保存原货物。                                     6、违约责任：按《中华人民共和国合同法》办。
3、合同变更：变更合同事宜，须在交货期前20天提出协商。                                   7、本合同供需双方签字或者盖章方可生效。';
		$pdf->Row(array('  通
	用
	条
	款',$str)); 
		
		$pdf->SetX(18);
        $pdf->SetWidths(array(15, 70, 15, 70, 15, 75)); 
        $pdf->Row(array('供货方', $salecomp['ComName'], '订货方', $buycomp['ComName'], '收货方', $buycomp['ComName']));

		$pdf->SetX(18);		
		$pdf->Row(array('地     址', $salecomp['Address'], '地     址', $buycomp['Address'], '地     址', $buycomp['Address']));

		$pdf->SetX(18);
        $pdf->SetWidths(array(15, 25,15,30, 15, 25,15,30, 15, 30,15,30)); 
        $pdf->Row(array('邮     编', $salecomp['postcode'], '代码', '','邮     编', $buycomp['postcode'], '代码', '','邮     编', $buycomp['postcode'], '代码', ''));

		$pdf->SetX(18);
        $pdf->Row(array('电     话', $salecomp['ContactTel'], '传真', $salecomp['ContactFax'],'电     话', $buycomp['ContactTel'], '传真', $buycomp['ContactFax'],'电     话', $buycomp['ContactTel'], '联系人', $buyuser['ARealName']));

		$pdf->SetX(18);
        $pdf->SetWidths(array(15, 70, 15, 70, 15, 75)); 
        $pdf->Row(array('开户行', $salecomp['BankType'], '开户行', $buycomp['BankType'], '运输方式', ''));

		$pdf->SetX(18);
        $pdf->Row(array('账     号', $salebank['MBR_SPE_ACCT_NO'], '账     号', $buybank['MBR_SPE_ACCT_NO'], '到港(站)', ''));

		$pdf->SetX(18); 
        $pdf->Row(array('税     号', $salecomp['TaxNo'], '税     号', $buycomp['TaxNo'], '专用线', ''));

		$pdf->SetX(20);
        $pdf->Write(10, '供方代表盖章：');
        $pdf->Write(10, '');

        $pdf->SetX(190);
        $pdf->Write(10, '需方代表盖章：');
        $pdf->Write(10, '');
        $pdf->Ln();

		$pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
	}


	//鞍钢合同模板
	public function angang($params)
    {
        $hth = $params['hth'];
		//$this->setvars();
        include("classfile.php");
        include("phpqrcode.php");

		

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select *,(BuyQuantity * SalesPrice) as totaljg from sm_contract_transaction_detail where TID = '" . $hth . "'");
		foreach($zydetail as &$tmp){
			$aa = $this->_dao->getRow("select * from  sm_exc_sales  where ID='".$tmp['Sid']."'");
			$tmp['ResourceNum']=$aa['ResourceNum'];
			$tmp['Standard ']=$aa['Standard'];
		}
		//总价格
		$totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail GROUP BY TID HAVING TID = '" . $hth . "'");

		//总数量
		$totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail GROUP BY TID HAVING TID = '" . $hth . "'");

		//品种
		$pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");

        //$new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
		//买方公司联系人
		$buyuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$buycomp['ID']."' and IsMain='1' limit 1 ");
		
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
		//卖方公司联系人
		$saleuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$salecomp['ID']."' and IsMain='1' limit 1 ");

        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese("L","mm", "A4");

        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true);

        $pdf->SetFont('simhei','',18);
		$pdf->SetX(80);
		$pdf->Write(20, '上海鞍钢国际贸易有限公司钢材、钢坯买卖合同');
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->SetX(20);
		$pdf->Write(10, '签订日期：');
        $pdf->Write(10, date("Y年m月d日",strtotime($contact['qdtime'])));

		$pdf->SetX(225);
        $pdf->Write(10, '合同号：');
        $pdf->Write(10, $contact['ContractNo']);
		$pdf->Ln();
/*
		$pdf->SetX(5);
		$pdf->Cell(15,12,'物料编码',1,0,'C');
		$pdf->Cell(26,12,'品名',1,0,'C');
		$pdf->Cell(27,12,'规格',1,0,'C');
		$pdf->Cell(22,12,'材质',1,0,'C');
*/

		
		$pdf->SetX(18);
        $pdf->SetWidths(array(30,40,35,20,30,15,15,25,25,25)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetvAligns("C"); $pdf->SetAligns("C");
        $pdf->Row(array('物料编码','品名','规格','材质','技术条件','数量','件数','含税结算价格(元/吨)','交货期','金额(元)')); 
       
        foreach($zydetail as $v)
        { 
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
			
            $pdf->Row(array($v['ResourceNum'],$v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['Standard '],$v['BuyQuantity'],$v['PerNumber'],$v['SalesPrice'],$contact['PickUpDate'],$v['totaljg'])); 
            
        } 

		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,40,35,20,30,15,15,25,25,25)); // 设置每列的宽度15,26,27,20,26,16,11,22,18,17
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计','','','','',$totalweight,'','','',$totalmoney)); 

		$pdf->SetX(18);
        $pdf->SetWidths(array(30,230)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
        $pdf->Row(array('结算方式',$GLOBALS['PAYTYPE'][$contact['PayType']])); 

																	
		$pdf->SetX(18);
		$pdf->SetWidths(array(9,251)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetvAligns("C");
		$str ='1、质量技术条件执行鞍钢现行标准，特殊技术要求需要在合同中注明。 2、计重方式、交货公差按鞍钢现行规定执行。 3、买方代办运输产品均参加国内货物运输保险、运费、保险费及运输部门收取的其它费用均由卖方承担，卖方代收，买方应按实发车种承担费用。   4、产品提出异议期限：自买方接到货物起7日内。 5、自提合同逾期不提，由卖方向买方收取保管费，收费标准按鞍钢现行规定执行。  6、买卖双方如需变更合同条款，应重新协商，签订变更合同。 7、其余未尽条款按合同法和国家现行的法规执行。 8、解决合同纠纷方式：双方协商，协商不成，由上海仲裁委员会仲裁。';
		$pdf->Row(array('条款',$str)); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(9,251)); 
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetvAligns("C");

		$str2 ='
出库费由提货方承担

';
		$pdf->Row(array('其他条款',$str2));
		$pdf->SetX(18);


		$pdf->Cell(9,48,'卖方',1);
		$pdf->Cell(96,6,'名称：'.$salecomp['ComName'],1);
		$pdf->Ln();

		$pdf->SetX(27);
		$pdf->Cell(96,6,'地址：'.$contact['ShipperAddress'],1);
		$pdf->Ln();


		$pdf->SetX(27);
		$pdf->Cell(60,6,'电话：'.$contact['ShipperPhone'],1);
		$pdf->Cell(36,6,'邮编：'.$contact['ShipperPostCode'],1);
		$pdf->Ln();
		$pdf->SetX(27);
		$pdf->Cell(96,6,'传真：'.$contact['ShipperFax'],1);
		$pdf->Ln();
		$pdf->SetX(27);
		$pdf->Cell(60,6,'开户行：'.$salecomp['BankType'],1);
		$pdf->Cell(36,6,'',1);
		$pdf->Ln();
		$pdf->SetX(27);
		$pdf->Cell(60,6,'账号：'.$salebank['MBR_SPE_ACCT_NO'],1);
		$pdf->Cell(36,6,'',1);
		$pdf->Ln();
		$pdf->SetX(27);
		$pdf->Cell(60,12,'法定代表人或委托代理人：',1);
		$pdf->Cell(36,12,$saleuser['ARealName'],1);

		
		$pdf->SetXY(123,60+$h);
		$pdf->Cell(9,48,'买方',1);
		$pdf->Cell(75,12,'名称：'.$buycomp['ComName'],1);
		$pdf->Ln();


		$pdf->SetXY(132,72+$h);
		$pdf->Cell(75,12,'地址：'.$contact['ConsigneeAddress'],1);
		$pdf->Ln();

		$pdf->SetXY(132,84+$h);
		$pdf->Cell(75,6,'联系人：'.$contact['ConsigneeMan'],1);
		$pdf->Ln();

		$pdf->SetXY(132,90+$h);
		
		$pdf->Cell(75,6,'提货仓库：'.$zydetail[0]['jhck'],1);
		$pdf->Ln();

/*
		$pdf->SetFont('simsun', 'B', 9); 
		$pdf->SetXY(88,102+$h);
		$pdf->Cell(40,12,'法定代表人或委托代理人：',1);
		$pdf->Cell(25,12,$saleuser['ARealName'],1);
*/

		$pdf->SetFont('simsun', 'B', 9); 
		$pdf->SetXY(132,96+$h);
		$pdf->Cell(50,12,'法定代表人或委托代理人：',1);
		$pdf->Cell(25,12,$buyuser['ARealName'],1);


		$pdf->SetXY(207,60+$h);
		$pdf->Cell(71,6,'联系电话：'.$contact['ConsigneePhone'],1);
		$pdf->Ln();
		$pdf->SetXY(207,66+$h);
		$pdf->Cell(71,6,'传真电话：'.$contact['ConsigneeFax'],1);
		$pdf->Ln();
		$pdf->SetXY(207,72+$h);
		$pdf->Cell(71,12,'邮编：'.$contact['ConsigneePostCode'],1);
		$pdf->Ln();
		$pdf->SetXY(207,84+$h);
		$pdf->Cell(71,6,'结算户头同买货单位',1);
		$pdf->Ln();
		$pdf->SetXY(207,90+$h);
		$pdf->Cell(71,6,'结算行：'.$buycomp['BankType'],1);
		$pdf->Ln();
		$pdf->SetXY(207,96+$h);
		$pdf->Cell(71,6,'账号：'.$buybank['MBR_SPE_ACCT_NO'],1);
		$pdf->Ln();
		$pdf->SetXY(207,102+$h);
		$pdf->Cell(71,6,'税号：'.$buycomp['TaxNo'],1);



/*

		$pdf->Cell(9,48,'买方',1);
		$pdf->Cell(65,6,'名称：'.$buycomp['ComName'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(65,6,'地址：'.$contact['ConsigneeAddress'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(40,6,'电话：'.$contact['ConsigneePhone'],1);
		$pdf->Cell(25,6,'邮编：'.$contact['ConsigneePostCode'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(65,6,'传真：'.$contact['ConsigneeFax'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(65,6,'开户行：'.$buycomp['BankType'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(65,6,'账号：'.$buybank['MBR_SPE_ACCT_NO'],1);
		$pdf->Ln();
		$pdf->SetX(14);
		$pdf->Cell(40,12,'法定代表人或委托代理人：',1);
		$pdf->Cell(25,12,$buyuser['ARealName'],1);

		$pdf->SetXY(79,66+$h);
		$pdf->Cell(9,48,'卖方',1);
		$pdf->Cell(65,12,'名称：'.$salecomp['ComName'],1);
		$pdf->Ln();

		$pdf->SetXY(88,78+$h);
		$pdf->Cell(65,12,'地址：'.$contact['ShipperAddress'],1);
		$pdf->Ln();
		$pdf->SetXY(88,90+$h);
		$pdf->Cell(65,6,'联系人：'.$contact['ShipperMan'],1);
		$pdf->Ln();
		//$pdf->SetXY(88,96+$h);
		//$pdf->SetFont('simsun', 'B', 6); 
		//$pdf->Cell(65,6,'提货仓库：'.$zydetail[0]['jhck'],1);
		//$pdf->Ln();
		$pdf->SetFont('simsun', 'B', 9); 
		$pdf->SetXY(88,102+$h);
		$pdf->Cell(40,12,'法定代表人或委托代理人：',1);
		$pdf->Cell(25,12,$saleuser['ARealName'],1);

		$pdf->SetXY(153,66+$h);
		$pdf->Cell(50,6,'联系电话：'.$contact['ShipperPhone'],1);
		$pdf->Ln();
		$pdf->SetXY(153,72+$h);
		$pdf->Cell(50,6,'传真电话：'.$contact['ShipperFax'],1);
		$pdf->Ln();
		$pdf->SetXY(153,78+$h);
		$pdf->Cell(50,12,'邮编：'.$contact['ShipperPostCode'],1);
		$pdf->Ln();
		$pdf->SetXY(153,90+$h);
		$pdf->Cell(50,6,'结算户头同买货单位',1);
		$pdf->Ln();
		$pdf->SetXY(88,96+$h);
		$pdf->Cell(115,6,'结算行：'.$salecomp['BankType'],1);
		$pdf->Ln();
		$pdf->SetXY(153,102+$h);
		$pdf->Cell(50,6,'账号：'.$salebank['MBR_SPE_ACCT_NO'],1);
		$pdf->Ln();
		$pdf->SetXY(153,108+$h);
		$pdf->Cell(50,6,'税号：'.$salecomp['TaxNo'],1);
*/
		$pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
	}

  
    //竞价打印
   public function bidding($params) 
   {
	   //echo $params['id'];
       include("classfile.php");
       include("phpqrcode.php");
     
      $buycp="";
		$sellcp="";
		//买家
		if($zy['SlType']=="1" || $zy['SlType']=="5" ){  //销售类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
		}else{//采购类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");	
			
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
		}

//供应商
$listgys = $this->_dao->Aquery("SELECT ComID,ComName FROM sys_user_mygys where 1 and ComType=1 and MyID=".$_SESSION['SYS_COMPANYID']." order by IsLsCj desc", "no");
/*echo "<pre>";
echo print_r($listgys);
echo "</pre>";
*/
$this->assign("listgys",$listgys);
$listgcht = $this->_dao->Aquery("SELECT SysNum,ConName  FROM sys_user_contract ", "no");
$this->assign("listgcht",$listgcht);
 //钢材取得顶级父类关键字
$this->assign("parentlist",$this->_dao->query( "SELECT id, ktype, kname FROM biz_key WHERE status = 1 AND parentid = 0 AND ktype=1" ));
$this->assign("secjsstr",$secjsstr);

/*//城市
$AddressCity = $this->_dao->getOne("select city from sys_company where ID = '".$_SESSION['SYS_COMPANYID']."' limit 1",0);
$this->assign("AddressCity",$AddressCity);
$AssureType = $this->_dao->getOne("select ComTransType from sys_company where ID = '".$_SESSION['SYS_COMPANYID']."' limit 1",0);
$this->assign("AssureType",$AssureType);
//担保类型
$AssureType = $this->_dao->getOne("select ComTransType from sys_company where ID = '".$_SESSION['SYS_COMPANYID']."' limit 1",0);
$this->assign("AssureType",$AssureType);
//可使用零保证金交易
$IsNoFinance = $this->_dao->getOne("select IsNoFinance from sys_company where ID = '".$_SESSION['SYS_COMPANYID']."' limit 1",0);
$this->assign("IsNoFinance",$IsNoFinance);
//授权交易会员类型
$empower = $this->_dao->getOne("select EmpowerType from sys_company where ID = '".$_SESSION['SYS_COMPANYID']."' limit 1",0);
$this->assign("empower",$empower);*/
$zy = $this->_dao->getRow("select sm_exc_sales_details.*,sm_exc_sales.* from sm_exc_sales left join sm_exc_sales_details on sm_exc_sales.ID=sm_exc_sales_details.Pid where sm_exc_sales.ID = '".$params['id']."'");
//print_r($zy);
$zy["parentid"]=$this->_dao->getOne( "SELECT parentid FROM biz_key WHERE status = 1 AND id = '".$zy["VarietyCode"]."'" );
$zy["flname"]=$this->_dao->getOne( "SELECT kname FROM biz_key WHERE status = 1 AND id = '".$zy["VarietyCode"]."'" );
if($zy["parentid"]==0 || $zy["parentid"]==""){
	$zy["parentid"]=$zy["VarietyCode"];
}

$this->assign("secparentlist",$this->_dao->query( "SELECT id, ktype, kname FROM biz_key WHERE status = 1 AND parentid = '".$zy["parentid"]."' AND ktype=1" ));

if($zy['SalesEndDate']> date("Y-m-d H:i:s")){
	$this->assign("todaytime",1);
}

$cklist = $this->_dao->query("SELECT * FROM `sm_user_address` where 1  and Mid = ".$_SESSION['SYS_COMPANYID']."  ", 0);
$this->assign( "AddressState", $GLOBALS['AddressState']);
$this->assign("cklist",$cklist);

$this->assign("zy",$zy);
	  $biglei = $this->_dao->getOne("select VarietyType from sm_base_variety  where  VarietyCode = '".$zy['VarietyCode']."'",0);
      $this->assign("biglei",$biglei);
	//  $pzs = $this->_dao->AQuery( "SELECT VarietyCode, VarietyName FROM sm_base_variety_big Where 1=1 ORDER BY ID",0 );
	//  $this->assign("pzs",$pzs);
$citys = $this->_dao->AQuery( "SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id",0 );
$this->assign("citys",$citys);
$this->assign("params",$params);

if($zy['Status'] == "2"){

$buylist = $this->_dao->query("select *,sm_exc_buy.ID as cid,sm_exc_buy.Status as cstatus,sm_exc_buy.CreateDate as cCreateDate from sm_exc_buy left join sys_company on sm_exc_buy.Mid = sys_company.ID where Sid = '".$params['id']."' ");
$this->assign("buylist",$buylist);

$maxmoney = $this->_dao->getOne("select max(PriceContention)  from sm_exc_buy where Sid = '".$params['id']."' and (Status = 1 or Status = 2)");
$minmoney = $this->_dao->getOne("select min(PriceContention)  from sm_exc_buy where Sid = '".$params['id']."' and (Status = 1 or Status = 2)");

$zuigaom = $this->_dao->query("select PriceContention  from sm_exc_buy where Sid = '".$params['id']."' and (Status = 1 or Status = 2) order by PriceContention desc limit 2");

$zuidim = $this->_dao->query("select PriceContention  from sm_exc_buy where Sid = '".$params['id']."' and (Status = 1 or Status = 2) and PriceContention >0 order by PriceContention asc limit 2");

	if(count($zuidim) < 2){
	$zuidim[1]['PriceContention'] = $zuidim[0]['PriceContention'];
	}

	if(count($zuigaom) < 2){
	$zuigaom[1]['PriceContention'] = $zuigaom[0]['PriceContention'];
	}


$this->assign("zuigaom",$zuigaom); 
$this->assign("zuidim",$zuidim);
$this->assign("maxmoney",$maxmoney);
$this->assign("minmoney",$minmoney);

$dgzbuyer = $this->_dao->getOne("select count(*) from sm_exc_buy where Sid = '".$params['id']."' and Status = 1");

$this->assign("dgzbuyer",$dgzbuyer);

}
$this->assign("jjtype",$GLOBALS['JINJIA_STATUS']);
$this->assign("danbao",$GLOBALS['DANBAO_TYPES']);

//页面默认值  交割日期
if($zy['SalesType']=='16'){
	$jgdata = date('Y-m-d',strtotime("+1 month"));
}else{
	$date = strtotime(date('Y-m-d'));
	$jgdata = date('Y-m-d',$date + 7*24*60*60);
}
$this->assign("jgdata",$jgdata);
$today = date('Y-m-d H:i:s');
$this->assign("enddate",$today);

       
	$pdf = new PDF_Chinese("L","mm", "A4");
		
	    $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
		
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true);
        
		$pdf->SetFont('simsun', 'B', 14); //设置字体样式
        $pdf->SetX(120);
        $pdf->Write(10,'报价单',0,0,C);
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
  if($zy['SalesType']=="1" || $zy['SalesType']=="5" ){
        $pdf->Write(10,'资源信息');
        }else{
		$pdf->Write(10,'需求信息');
		}
		$pdf->Ln();
	
		$pdf->SetX(18);   
        //updated by quanjw for tks start 2016/9/7
		//$pdf->Cell(38,8,'品 名',1,0,'C');
		//$pdf->Cell(39,8,'材质',1,0,'C');
        //$pdf->Cell(39,8,'规 格',1,0,'C');
        $k = $zy['Vid'];
        if( $k == $GLOBALS["MEITAN_VID"]){
            $pdf->Cell(29,8,'品 名',1,0,'C');
            $pdf->Cell(29,8,'挥发分',1,0,'C');
            $pdf->Cell(29,8,'灰 分',1,0,'C');
            $pdf->Cell(29,8,'全硫分',1,0,'C');
        }else if( $k == $GLOBALS["JIAOTAN_VID"]){
            $pdf->Cell(29,8,'品 名',1,0,'C');
            $pdf->Cell(29,8,'灰 分',1,0,'C');
            $pdf->Cell(29,8,'硫 分',1,0,'C');
            $pdf->Cell(29,8,'C S R',1,0,'C');
        }else if( $k == $GLOBALS["SHUINI_VID"]){
            $pdf->Cell(58,8,'品 名',1,0,'C');
            $pdf->Cell(58,8,'规 格',1,0,'C');
        }else if( $k == $GLOBALS["JINSHU_VID"]){
            $pdf->Cell(29,8,'品 名',1,0,'C');
            $pdf->Cell(29,8,'规 格',1,0,'C');
            $pdf->Cell(29,8,'强 度',1,0,'C');
            $pdf->Cell(29,8,'锌层重量',1,0,'C');
        }else if( $k == $GLOBALS["TKS_VID"]){
            $pdf->Cell(29,8,'品 名',1,0,'C');
            $pdf->Cell(29,8,'Fe%',1,0,'C');
            $pdf->Cell(29,8,'SiO2%',1,0,'C');
            $pdf->Cell(29,8,'AL2O3%',1,0,'C');
        }else{
            $pdf->Cell(38,8,'品 名',1,0,'C');
            $pdf->Cell(39,8,'规 格',1,0,'C');
            $pdf->Cell(39,8,'材 质',1,0,'C');
        }
		//updated by quanjw for tks end 2016/9/7
        
        $pdf->Cell(39,8,'产地（厂家）',1,0,'C');
		$pdf->Cell(39,8,'单 价（元/吨）',1,0,'C');
        $pdf->Cell(39,8,'重 量（吨）',1,0,'C');
		$pdf->Ln();	
	
		$pdf->SetX(18);
        //$pdf->SetWidths(array(38,39,39,39,39,39,39));
        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', '', 10); 
        $pdf->SetAligns("C"); 
		$A=$pdf->GetX(18);
        
        $zy['VarietyName'] = html_entity_decode($zy['VarietyName']);
        $zy['MaterialCode'] = html_entity_decode($zy['MaterialCode']);
        $zy['SpecCode'] = html_entity_decode($zy['SpecCode']);
        $zy['strength'] = html_entity_decode($zy['strength']);
        $zy['xincengWeight'] = html_entity_decode($zy['xincengWeight']);
        //updated by quanjw for tks start 2016/9/7
        //$pdf->Row(array($zy['VarietyName'],$zy['MaterialCode'],$zy['SpecCode'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['Tweight']));
        if( $k == $GLOBALS["MEITAN_VID"]){
           $pdf->SetWidths(array(29,29,29,29,39,39,39,39));
           $pdf->Row(array($zy['VarietyName'],$zy['MaterialCode'],$zy['SpecCode'],$zy['cd'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }else if( $k == $GLOBALS["JIAOTAN_VID"]){
            $pdf->SetWidths(array(29,29,29,29,39,39,39,39));
            $pdf->Row(array($zy['VarietyName'],$zy['SpecCode'],$zy['MaterialCode'],$zy['cd'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }else if( $k == $GLOBALS["SHUINI_VID"]){
            $pdf->SetWidths(array(58,58,39,39,39,39));
            $pdf->Row(array($zy['VarietyName'],$zy['SpecCode'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }else if( $k == $GLOBALS["JINSHU_VID"]){
            $pdf->SetWidths(array(29,29,29,29,39,39,39,39));
            $pdf->Row(array($zy['VarietyName'],$zy['SpecCode'],$zy['strength'],$zy['xincengWeight'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }else if( $k == $GLOBALS["TKS_VID"]){
            $pdf->SetWidths(array(29,29,29,29,39,39,39,39));
            $pdf->Row(array($zy['VarietyName'],$zy['tks_fe'],$zy['tks_si'],$zy['tks_al'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }else{
            $pdf->SetWidths(array(38,39,39,39,39,39,39));
            $pdf->Row(array($zy['VarietyName'],$zy['MaterialCode'],$zy['SpecCode'],$zy['OriginCode'],$zy['SalesMinPrice'],$zy['QuantitySales']));
        }
        //updated by quanjw for tks start 2016/9/7
        
        $pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'付款与交割信息'); 
		$pdf->Ln();
        
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
		$fkfs=$GLOBALS['PAYTYPE'];
		if($temp['IsTalk']=="1"){
	    $pdf->Row(array('付款方式：',$fkfs[$zy['fkfs']],'预付款比例：',$zy['LyMoney'],'是否可洽谈：','可洽谈')); 
		}else{
		$fkfs=$GLOBALS['PAYTYPE'];
	    $pdf->Row(array('付款方式：',$fkfs[$zy['fkfs']],'预付款比例：',$zy['LyMoney'],'是否可洽谈：','不可洽谈')); 
		}

        $pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
		$Delivery=$GLOBALS['DELIVERY'];
		$PickUpType=$GLOBALS['JIAOGE_TYPES'];
		$temp=$zy['StoreCity']?$zy['StoreCity']:$citys[$zy['StoreCityCode']];
        $pdf->Row(array('提货方式：',$Delivery[$zy['Delivery']],'所在城市：',$temp,'交割类型：',$PickUpType[$zy['PickUpType']]));

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('交割日期：',$zy['PickUpDate'],'交割城市：',$zy['PickUpCity'],'交货仓库：',$zy['jhck']));

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,203)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('交货地址：',$zy['PickUpAddress']));
		$pdf->Ln();

		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'联系人信息'); 
		$pdf->Ln();

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('联系人姓名：',$zy['ContactMan'],'手机号码：',$zy['ContactMobile'],'固定号码：',$zy['ContactPhone']));

        $pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('传 真：',$zy['ContactFax'],'电子邮件：',$zy['ContactEmail'],'QQ号：',$zy['QQNum']));
		$pdf->Ln();

		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'竞价清单'); 
		$pdf->Ln();
   /*foreach ($buylist as $v){
		$pdf->SetX(18);   
		$pdf->Cell(43,8,'公司名称',1,0,'C');
		$pdf->Cell(38,8,'担保类型',1,0,'C');
		$pdf->Cell(38,8,'单价（元/吨）',1,0,'C');
	if($zy['SalesType']=="1" || $zy['SalesType']=="5" ){
        $pdf->Cell(38,8,'采购数量',1,0,'C');
	}else{
        $pdf->Cell(38,8,'供应数量',1,0,'C');
	}
	    $pdf->Cell(38,8,'总价',1,0,'C');
        $pdf->Cell(38,8,'报价时间',1,0,'C');
        $pdf->Ln();

		$pdf->SetX(18);
        $pdf->SetWidths(array(43,38,38,38,38,38));
        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', '', 10); 
        $pdf->SetAligns("C");
		$AssureType=$GLOBALS['AssureType'];
		$danbao=$GLOBALS['DANBAO_TYPES'];
		$A=$pdf->GetX();
        $pdf->Row(array($v['ComNameShort'],$danbao[$v['ComTransType']],$v['PriceContention'],$v['BuyQuantity'],$zy['Tmoney'],$zy['CreateDate']));
		$pdf->Ln();


		$pdf->Output($temp['OrderNo']. '.pdf', 'I'); //D or I
     }*/
	 //added by hzp for jingjia dayin started 2015/06/12
	 $pdf->SetX(18);   
		$pdf->Cell(43,8,'公司名称',1,0,'C');
		$pdf->Cell(38,8,'担保类型',1,0,'C');
		$pdf->Cell(38,8,'单价（元/吨）',1,0,'C');
	if($zy['SalesType']=="1" || $zy['SalesType']=="5" ){
        $pdf->Cell(38,8,'采购数量',1,0,'C');
	}else{
        $pdf->Cell(38,8,'供应数量',1,0,'C');
	}
	    $pdf->Cell(38,8,'总价',1,0,'C');
        $pdf->Cell(38,8,'报价时间',1,0,'C');
        $pdf->Ln();
		
	foreach($buylist as $v){
			$AssureType=$GLOBALS['AssureType'];
			$danbao=$GLOBALS['DANBAO_TYPES'];
			$pdf->SetX(18);
			$pdf->SetWidths(array(43,38,38,38,38,38));
            $pdf->SetZeilenhoehe("8");
            $pdf->SetFont('simsun', '', 11); //设置字体样式
            $pdf->SetAligns("center");

            $pdf->Row(array($v['ComNameShort'],$danbao[$v['ComTransType']],$v['PriceContention'],$v['BuyQuantity'],$zy['Tmoney'],$v['cCreateDate'])); // 设置列名    
        } 


		
		$pdf->Output($temp['OrderNo']. '.pdf', 'I'); //D or I
		//added by hzp for jingjia dayin ended 2015/06/12
  }   
    

	
	//added by hezp for 17307 started 2015/10/21
		//打包竞价打印
   public function jjtc_print($params) 
   {
		//echo $params['id'];
		include("classfile.php");
		include("phpqrcode.php");
		
		//资源信息
		$zyl = $this->_dao->query("select * from sm_exc_sales_details where Pid = '".$params['id']."' ");
        //added by quanjw for tks 
        $zyl = $this->zy_format( $zyl );
		//added by quanjw for tks 
        
        //付款与交割信息
		$zy = $this->_dao->getRow("select sm_exc_sales_details.*,sm_exc_sales.* from sm_exc_sales left join sm_exc_sales_details on sm_exc_sales.ID=sm_exc_sales_details.Pid where sm_exc_sales.ID = '".$params['id']."'");
        
		$citys = $this->_dao->AQuery( "SELECT cityid as CityCode, cityname as CityName FROM city Where 1=1 ORDER BY id",0 );

		//竞价信息
		$blist = $this->_dao->query("SELECT * FROM sm_exc_buy_tag as a where  Tid = '".$params['id']."' ORDER BY id  DESC", "no");
			$stanum=0;		
			for($i=0;$i<count($blist);$i++){
				if($blist[$i]["Status"]=="22"){
					$stanum++;
				}
		
				$comname = $this->_dao->getOne("select ComName from sys_company where ID = '".$blist[$i]["Mid"]."'");
				$bid_id=$blist[$i]["ID"];
		
				$blist[$i]["Comname"]=$comname;				
					if($bid_id!=""){
						$sql="SELECT count(*) FROM sm_exc_buy where Btid='".$bid_id."'  ";
						$arr_req=$this->_dao->getone($sql);
						$blist[$i]["isrequst"]=$arr_req;
						 $sql="SELECT * FROM sm_exc_buy left join sm_exc_sales_details on sm_exc_buy.sdid = sm_exc_sales_details.ID where Btid='".$bid_id."'";
						//updated by quanjw for tks 
                        $arr_temp = $this->_dao->query($sql);
                        $arr_mx = $this->zy_format( $arr_temp ) ;
                        //updated by quanjw for tks 
							for($j=0;$j<count($arr_mx);$j++){
								$VarietyCode=$arr_mx[$j]["VarietyCode"];		
								if($VarietyCode!=""){
								$sql="SELECT VarietyName FROM `sm_base_variety` where VarietyCode='".$VarietyCode."'";
								$VarietyName=$this->_dao->getone($sql);
								$arr_mx[$j]["vcodename"]=$VarietyName;
								}				
							}
							$blist[$i]["arr_mx"]=$arr_mx;	 
					}							
			}

       
		$pdf = new PDF_Chinese("L","mm", "A4");
		
	    $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
		
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,25);

        
		$pdf->SetFont('simsun', 'B', 14); //设置字体样式
        $pdf->SetX(120);
        $pdf->Write(10,'报价单',0,0,C);
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
  if($zy['SalesType']=="1" || $zy['SalesType']=="5" ){
        $pdf->Write(10,'资源信息');
        }else{
		$pdf->Write(10,'需求信息');
		}
		$pdf->Ln();
	
		$pdf->SetX(18);   
		$pdf->Cell(38,8,'品 名',1,0,'C');
		$pdf->Cell(39,8,'材质',1,0,'C');
		$pdf->Cell(39,8,'规 格',1,0,'C');
        $pdf->Cell(39,8,'产地（厂家）',1,0,'C');
		$pdf->Cell(39,8,'单 价（元/吨）',1,0,'C');
        $pdf->Cell(39,8,'重 量（吨）',1,0,'C');
		$pdf->Ln();	
	foreach($zyl as $v){
		$pdf->SetX(18);
        $pdf->SetWidths(array(38,39,39,39,39,39,39));
        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', '', 10); 
        $pdf->SetAligns("C"); 
		$A=$pdf->GetX(18);
        $pdf->Row(array($v['VarietyName'],$v['MaterialCode'],$v['SpecCode'],$v['OriginCode'],$v['SalesMinPrice'],$v['QuantitySales']));
	}
        $pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'付款与交割信息'); 
		$pdf->Ln();
        
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
		$fkfs=$GLOBALS['PAYTYPE'];
		if($zy['IsTalk']=="1"){
	    $pdf->Row(array('付款方式：',$fkfs[$zy['fkfs']],'预付款比例：',$zy['LyMoney'],'是否可洽谈：','可洽谈')); 
		}else{
		$fkfs=$GLOBALS['PAYTYPE'];
	    $pdf->Row(array('付款方式：',$fkfs[$zy['fkfs']],'预付款比例：',$zy['LyMoney'],'是否可洽谈：','不可洽谈')); 
		}

        $pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");
		$Delivery=$GLOBALS['DELIVERY'];
		$PickUpType=$GLOBALS['JIAOGE_TYPES'];
		$temp=$zy['StoreCity']?$zy['StoreCity']:$citys[$zy['StoreCityCode']];
        $pdf->Row(array('提货方式：',$Delivery[$zy['Delivery']],'所在城市：',$temp,'交割类型：',$PickUpType[$zy['PickUpType']]));

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('交割日期：',$zy['PickUpDate'],'交割城市：',$zy['PickUpCity'],'交货仓库：',$zy['jhck']));

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,203)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('交货地址：',$zy['PickUpAddress']));
		$pdf->Ln();

		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'联系人信息'); 
		$pdf->Ln();

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('联系人姓名：',$zy['ContactMan'],'手机号码：',$zy['ContactMobile'],'固定号码：',$zy['ContactPhone']));

        $pdf->SetX(18);
		$pdf->SetWidths(array(30,48,30,47,30,48)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("8");
        $pdf->SetFont('simsun', 'B', 10); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('传 真：',$zy['ContactFax'],'电子邮件：',$zy['ContactEmail'],'QQ号：',$zy['QQNum']));
		$pdf->Ln();

		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'竞价清单'); 
		$pdf->Ln();
   
		$pdf->SetX(18);   
		$pdf->Cell(45,8,'竞价公司',1,0,'C');
		$pdf->Cell(47,8,'总价',1,0,'C');
		$pdf->Cell(47,8,'担保类型',1,0,'C');
	
	    $pdf->Cell(47,8,'竞价日期',1,0,'C');
        $pdf->Cell(47,8,'状态',1,0,'C');
        $pdf->Ln();
		$dw ='吨';
		
		foreach($blist as $v){
			$jjtype=$GLOBALS['JINJIA_STATUS'];
			$danbao=$GLOBALS['DANBAO_TYPES'];
			$pdf->SetX(18);
			$pdf->SetWidths(array(45,47,47,47,47));
            $pdf->SetZeilenhoehe("8");
            $pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetAligns("center");

            $pdf->Row(array($v['Comname'],$v['Tmoney'],$danbao[$v['AssureType']],$v['CreateDate'],$jjtype[$v['Status']])); // 设置列名 
			
			$pdf->SetX(18);   
			$pdf->Cell(35,8,'品名',1,0,'C');
			$pdf->Cell(33,8,'材质',1,0,'C');
			$pdf->Cell(33,8,'规格',1,0,'C');
		
			$pdf->Cell(33,8,'厂家',1,0,'C');
			$pdf->Cell(33,8,'报价',1,0,'C');
			$pdf->Cell(33,8,'单位',1,0,'C');
			$pdf->Cell(33,8,'重量',1,0,'C');
			$pdf->Ln();
			foreach($v['arr_mx'] as $vv){
				$pdf->SetX(18);
				$pdf->SetWidths(array(35,33,33,33,33,33,33));
				$pdf->SetZeilenhoehe("8");
				$pdf->SetFont('simsun', 'B', 10); //设置字体样式
				$pdf->SetAligns("center");

				$pdf->Row(array($vv['VarietyName'],$vv['MaterialCode'],$vv['SpecCode'],$vv['OriginCode'],$vv['PriceContention'],$dw,$vv['BuyQuantity'])); // 设置列名 

			}
			$pdf->Ln();
        } 


		
		$pdf->Output($temp['OrderNo']. '.pdf', 'I'); //D or I
		
	}   
	//added by hezp for 17307 ended 2015/10/21
	
	//发货单
    public function send($params)
    {  
          include("classfile.php");
          include("phpqrcode.php");

		$td = $this->_dao->getRow("select * from sm_contract_transaction_td where Tid = '". $params['id']."'");
		//仓库名
		$ck = $td['Ck'];
        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");

        
		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$params['id']."'");
		

        $buycp="";
		$sellcp="";
		//买家
		if($zy['SlType']=="1" || $zy['SlType']=="5" ){  //销售类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
		}else{//采购类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");	
			
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
		}
		
		


		
$Allsl=0;

$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order  where   Did='".$params['id']."'");
foreach($zydetail as $val){
	
	//Updatedd by quanjw for mejjiao start 2015/7/17 关联sm_exc_sales_details 获取Vid 判断资源类型
	//$pzzlist[$val['oid']] = $this->_dao->query("select *,sm_exc_order_detail.* from sm_exc_order_detail,sm_exc_dd where sm_exc_dd.ID=sm_exc_order_detail.Ddid and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
	//$pzzlist[$val['oid']] = $this->_dao->query("select sm_exc_dd.*,sm_exc_order_detail.*,sm_exc_sales_details.Vid,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight from sm_exc_order_detail,sm_exc_dd ,sm_exc_sales_details where sm_exc_dd.ID=sm_exc_order_detail.Ddid and sm_exc_dd.sdid = sm_exc_sales_details.id and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
    $pzzlist[$val['oid']] = $this->_dao->query("select sm_exc_dd.*,sm_exc_order_detail.*,sm_exc_sales_details.Vid,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.tks_fe,sm_exc_sales_details.tks_si,sm_exc_sales_details.tks_al from sm_exc_order_detail,sm_exc_dd ,sm_exc_sales_details where sm_exc_dd.ID=sm_exc_order_detail.Ddid and sm_exc_dd.sdid = sm_exc_sales_details.id and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
	//Updatedd by quanjw for mejjiao end 2015/7/17
	
	$pzzlist2[$val['oid']] = $this->_dao->query("select *,sm_exc_order_detail.Status as Status2 from sm_exc_dd,sm_exc_order_detail where sm_exc_dd.ID=sm_exc_order_detail.Ddid and  Oid = '".$val['oid']."' order by sm_exc_order_detail.ID ASC",0);
	$Allsl = $Allsl+$val['Fhsl'];
	
}	




/*$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order,sm_exc_dd  where sm_exc_order.Did = sm_exc_dd.Dtid and sm_exc_order.Sdid = sm_exc_dd.sdid  and   Did='".$params['id']."'");
foreach($zydetail as $val){
	$pzzlist[$val['oid']] = $this->_dao->query("select * from sm_exc_order_detail where Oid = '".$val['oid']."' order by ID ASC",0);
	//echo "select * from sm_exc_order_detail where Oid = '".$val['oid']."' order by ID ASC";

	$Allsl = $Allsl+$val['Fhsl'];
}*/





//运输公司

		$yscom = $this->_dao->query("select *  from sm_user_trans where Mid ='".$_SESSION['SYS_COMPANYID']."' and Status=1");



		if(empty($yscom)){
		$this->assign("nullyscom",1);
		}

		$yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");


		$ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '".$params['id']."'",0);


		$id = $ht['BID'];

		$MaxNum=$this->_dao->GetOne("select count(*) from sm_exc_order  where   Did='".$params['id']."'");
		
		
		
		$pdf = new PDF_Chinese("L","mm", "A4");
       
		

	   $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,15);
		
		$temp=0;
	
		
		foreach($zydetail as $v)
	{
		
		$temp++;
        $pdf->SetFont('simhei','',12);
		//$pdf->SetX(40);
		//$pdf->Cell(0,10,'上海鞍钢国际贸易有限公司('.$ck.')钢材发货单',0,0,C); 
		//$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
		$pdf->Cell(0,10,$sellcp['ComName'].'   发货单',0,0,C); 
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'日期：');
        $pdf->Write(10, date("Y年m月d日"));
		$pdf->Ln();
	
		$pdf->SetX(18);
		$pdf->SetWidths(array(25,95,25,90)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("B");		
        $pdf->Row(array('采购方',$buycp['ComName'],'供应方',$sellcp['ComName']));
		$pdf->Ln();
      
         

		  
        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'发货信息：'); 
		$pdf->Ln();

	/*foreach($zydetail as $v)
		{
      */  
         
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
	    $pdf->Row(array('发货单号',$v['FhNum'],'发货日期',$v['FhDate'],'车牌号',$v['Carnum'])); 
		
		
		if($ht['Delivery']!=1){
			$pdf->SetX(18);
			$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
			$pdf->SetZeilenhoehe("6");
			$pdf->SetFont('simsun', 'B', 9); 
			$pdf->SetAligns("C");	
			$pdf->Row(array('联系人',$v['Fhlxr'],'联系电话',$v['Fhphone'],'运输公司',$yscom2[$v['Yscom']]));
		}
        //$pdf->Row(array('运输公司',$yscom2[$zydetail[0]['Yscom']],'联系人',$zydetail[0]['Fhlxr'],'联系电话',$zydetail[0]['Fhphone'])); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,205)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('备注',$v['Fhbz']));

        $pdf->Ln();
		
		
		//alert($v.oid);
		//UPdated by quanjw for meijiao start 2015/7/17  发货单 区分资源类型  
		
		$zyll = $this->groupResourse($pzzlist[$v['oid']]);
		//file_put_contents("123.txt",print_r($zyll,true));

		
		//总计
		$alljs=0;
		$allsl=0;
		foreach($zyll as $k => $res)
        {
			$pdf->SetX(18);
			//$pdf->Cell(25,12,'物料编码',1,0,'C');   
			
			//输出分类资源 长度一致
			if( $k == $GLOBALS["MEITAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'挥发分',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(18,10,'全硫分',1,0,'C');
			}else if( $k == $GLOBALS["JIAOTAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(19,10,'硫 分',1,0,'C');
				$pdf->Cell(18,10,'C S R',1,0,'C');
			}else if( $k == $GLOBALS["SHUINI_VID"]){
				$pdf->Cell(38,10,'品 名',1,0,'C');
				$pdf->Cell(37,10,'规 格',1,0,'C');
			}else if( $k == $GLOBALS["JINSHU_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'规 格',1,0,'C');
				$pdf->Cell(19,10,'强 度',1,0,'C');
				$pdf->Cell(18,10,'锌层重量',1,0,'C');
			}else if( $k == $GLOBALS["TKS_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'Fe%',1,0,'C');
				$pdf->Cell(19,10,'SiO2%',1,0,'C');
				$pdf->Cell(18,10,'AL2O3%',1,0,'C');
			}else{
				$pdf->Cell(25,10,'品 名',1,0,'C');
				$pdf->Cell(25,10,'规 格',1,0,'C');
				$pdf->Cell(25,10,'材 质',1,0,'C');
			}
			//Added by hezp for 17307 ended 2015/10/15
			
			$pdf->Cell(25,10,'产地（厂家）',1,0,'C');
			$pdf->Cell(20,10,'件 重',1,0,'C');
			$pdf->Cell(20,10,'件 数',1,0,'C');
			$pdf->Cell(20,10,'数 量',1,0,'C');
			$pdf->Cell(25,10,'生产日期',1,0,'C');
			$pdf->Cell(25,10,'质保书',1,0,'C');
			$pdf->Cell(25,10,'货号',1,0,'C');
			$pdf->Ln();
			
			//小计
			$js=0;
			$sl=0;
			foreach($res as $vv){
				

				$pdf->SetX(18);
				$pdf->SetLeftMargin(18);
				$pdf->SetZeilenhoehe("5");
				$pdf->SetFont('simsun', '', 8); 
				$pdf->SetAligns("C");


				$A=$pdf->GetX();
				$pdf->SetAligns("C");
				//$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

				
				if( $k == $GLOBALS["MEITAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['cd'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['caizhi'],$vv['cd'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["SHUINI_VID"]){
					$pdf->SetWidths(array(38,37,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JINSHU_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['strength'],$vv['xincengWeight'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["TKS_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['tks_fe'],$vv['tks_si'],$vv['tks_al'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}else{
					$pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}
				
				$js=$js+$vv['Fhjs'];
				$sl=$sl+$vv['Fhsl'];
			}
			
			//小计
			if(!empty($res)){
				
				$pdf->SetX(18);
				$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");		
				$pdf->Row(array('小计',$js,$sl,'','',''));  
				
			
			}
			$alljs=$alljs+$js;
			$allsl=$allsl+$sl;
        }
		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('总计',$alljs,$allsl,'','',''));  		
	
		if($temp<$MaxNum){
			$pdf->AddPage();
		}
	
	//Updated by quanjw for meijiao start 2015/7/17		
		
		/* $pdf->SetX(18);   
		$pdf->Cell(25,10,'品 名',1,0,'C');
		$pdf->Cell(25,10,'规 格',1,0,'C');
		$pdf->Cell(25,10,'材 质',1,0,'C');
        $pdf->Cell(25,10,'产地（钢厂）',1,0,'C');
        $pdf->Cell(20,10,'件 重',1,0,'C');
		$pdf->Cell(20,10,'件 数',1,0,'C');
        $pdf->Cell(20,10,'数 量',1,0,'C');
	    $pdf->Cell(25,10,'生产日期',1,0,'C');
		$pdf->Cell(25,10,'质保书',1,0,'C');
	    $pdf->Cell(25,10,'货号',1,0,'C');
		 $pdf->Ln();
		 
		

     $js=0;
	 $sl=0;
    foreach($pzzlist[$v['oid']] as $vv){

     
        $pdf->SetX(18);
        $pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));

        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
         $pdf->SetZeilenhoehe("5");
         $pdf->SetFont('simsun', '', 8); 
         $pdf->SetAligns("C");
        
		  
		 $A=$pdf->GetX();
		  $pdf->SetAligns("C");
         $pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

		  $js=$js+$vv['Fhjs'];
	       $sl=$sl+$vv['Fhsl'];
	  }

        $pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('小计',$js,$sl,'','',''));  

		if($temp<$MaxNum)
			$pdf->AddPage();

		
		
	}*/
	/*
		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(230);
		$pdf->SetY(-100);
		$pdf->Write(10,'总计：');
        $pdf->Write(10, '  '.$Allsl."  吨");
		$pdf->Ln();

*/
       /* 

		


	 
     $js=0;
	 $sl=0;
     foreach($zydetail as &$v){
		 //alert("first.");
        //foreach($pzzlist as &$vv)
        //{   // alert("12");
		    //alert("hello");
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
		  $A=$pdf->GetX();
          $pdf->Row(array($v['pinming'],$v['caizhi'],$v['guige'],$v['Factory'],$v['Fhjz'],$v['Fhjs'],$v['Fhsl'],$v['MfgDate'],$v['zbs'],$v['FhNum'])); 
            
			
		   $js=$js+$v['Fhjs'];
	       $sl=$sl+$v['Fhsl'];
       // } 
     }
		$h = $pdf->GetY();
*/

	}	
		$pdf->Output($zy['OrderNo']. '.pdf', 'I'); //D or I

	}
  
	




 //验收单(包括收货单和发货单)

      public function receive($params)
    {   
          include("classfile.php");
          include("phpqrcode.php");


        $td = $this->_dao->getRow("select * from sm_contract_transaction_td where Tid = '". $params['id']."'");
		//仓库名
		$ck = $td['Ck'];
        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");

        
		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$params['id']."'");
		


		//买家
		$buycp="";
		$sellcp="";
		if($zy['SlType']=="1" || $zy['SlType']=="5" ){  //销售类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
		}else{//采购类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");	
			
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
		}
		
		


		
  

$Allsh=0;
$Allfh=0;


$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order  where   Did='".$params['id']."'");
foreach($zydetail as $val){
	
	//Updated by quanjw for meijiao start 2015/7/20
	//$pzzlist[$val['oid']] = $this->_dao->query("select * from sm_exc_dd,sm_exc_order_detail where sm_exc_dd.ID=sm_exc_order_detail.Ddid and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
	$pzzlist[$val['oid']] = $this->_dao->query("select sm_exc_dd.* ,sm_exc_order_detail.*,sm_exc_sales_details.Vid,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.cd,sm_exc_sales_details.tks_fe,sm_exc_sales_details.tks_si,sm_exc_sales_details.tks_al from sm_exc_dd,sm_exc_order_detail,sm_exc_sales_details where sm_exc_dd.ID=sm_exc_order_detail.Ddid and sm_exc_sales_details.id = sm_exc_dd.sdid and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
	//Updated by quanjw for meijiao end 2015/7/20
	$Allsh = $Allsh+$val['Shsl'];
	$Allfh = $Allfh+$val['Fhsl'];
}

/*
$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order,sm_exc_dd  where sm_exc_order.Did = sm_exc_dd.Dtid and sm_exc_order.Sdid = sm_exc_dd.sdid  and   Did='".$params['id']."'");
foreach($zydetail as $val){
	$pzzlist[$val['oid']] = $this->_dao->query("select * from sm_exc_order_detail where Oid = '".$val['oid']."' order by ID ASC",0);
	$Allsh = $Allsh+$val['Shsl'];
	$Allfh = $Allfh+$val['Fhsl'];
}*/


//运输公司

$yscom = $this->_dao->query("select *  from sm_user_trans where Mid ='".$_SESSION['SYS_COMPANYID']."' and Status=1");



if(empty($yscom)){
$this->assign("nullyscom",1);
}

$yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");



$ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '".$params['id']."'",0);

		
		
		$pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,15);

        $pdf->SetFont('simhei','',12);
		//$pdf->SetX(40);
		//$pdf->Cell(0,10,'上海鞍钢国际贸易有限公司('.$ck.')钢材收货单',0,0,C); 
		$pdf->Cell(0,10,$buycp['ComName'].'   验收单',0,0,C); 
		//$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'日期：');
        $pdf->Write(10, date("Y年m月d日"));
		$pdf->Ln();


        $pdf->SetX(18);
		$pdf->SetWidths(array(25,95,25,90)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("B");		
        $pdf->Row(array('采购方',$buycp['ComName'],'供应方',$sellcp['ComName']));
       // $pdf->SetX(18);
		//$pdf->Cell(235,7,'结算信息',1,0,'C');
       $pdf->Ln();
	 
		
        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'发货信息：'); 
		$pdf->Ln();


	 foreach($zydetail as $v){//发货单信息
        
        $pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
	    $pdf->Row(array('发货单号',$v['FhNum'],'发货日期',$v['FhDate'],'车牌号',$v['Carnum'])); 
		if($ht['Delivery']!=1){
        $pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('联系人',$v['Fhlxr'],'联系电话',$v['Fhphone'],'运输公司',$yscom2[$v['Yscom']]));
		}

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,205)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('备注',$v['Fhbz']));

       


        $pdf->Ln();
		
		
		//UPdated by quanjw for meijiao start 2015/7/17  发货单 区分资源类型  
		
		$zyll = $this->groupResourse($pzzlist[$v['oid']]);
		//file_put_contents("123.txt",print_r($zyll,true));
		
		$alljs=0;
		$allsl=0;
		foreach($zyll as $k => $res)
        {
			$pdf->SetX(18);
			//$pdf->Cell(25,12,'物料编码',1,0,'C');   
			
			//输出分类资源 长度一致
			if( $k == $GLOBALS["MEITAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'挥发分',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(18,10,'全硫分',1,0,'C');
			}else if( $k == $GLOBALS["JIAOTAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(19,10,'硫 分',1,0,'C');
				$pdf->Cell(18,10,'C S R',1,0,'C');
			}else if( $k == $GLOBALS["SHUINI_VID"]){
				$pdf->Cell(38,10,'品 名',1,0,'C');
				$pdf->Cell(37,10,'规 格',1,0,'C');
			}else if( $k == $GLOBALS["JINSHU_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'规 格',1,0,'C');
				$pdf->Cell(19,10,'强 度',1,0,'C');
				$pdf->Cell(18,10,'锌层重量',1,0,'C');
			}else if( $k == $GLOBALS["TKS_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'Fe%',1,0,'C');
				$pdf->Cell(19,10,'SiO2%',1,0,'C');
				$pdf->Cell(18,10,'AL2O3%',1,0,'C');
			}else{
				$pdf->Cell(25,10,'品 名',1,0,'C');
				$pdf->Cell(25,10,'规 格',1,0,'C');
				$pdf->Cell(25,10,'材 质',1,0,'C');
			}
			//Added by hezp for 17307 ended 2015/10/15
			
			$pdf->Cell(25,10,'产地（厂家）',1,0,'C');
			$pdf->Cell(20,10,'件 重',1,0,'C');
			$pdf->Cell(20,10,'件 数',1,0,'C');
			$pdf->Cell(20,10,'数 量',1,0,'C');
			$pdf->Cell(25,10,'生产日期',1,0,'C');
			$pdf->Cell(25,10,'质保书',1,0,'C');
			$pdf->Cell(25,10,'货号',1,0,'C');
			$pdf->Ln();
			
			//小计
			$js=0;
			$sl=0;
			foreach($res as $vv){
				

				$pdf->SetX(18);
				$pdf->SetLeftMargin(18);
				$pdf->SetZeilenhoehe("5");
				$pdf->SetFont('simsun', '', 8); 
				$pdf->SetAligns("C");


				$A=$pdf->GetX();
				$pdf->SetAligns("C");
				//$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

				
				if( $k == $GLOBALS["MEITAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['cd'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['caizhi'],$vv['cd'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["SHUINI_VID"]){
					$pdf->SetWidths(array(38,37,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
			    }elseif( $k == $GLOBALS["TKS_VID"]){
                    $pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25)); // 设置每列的宽度
                    $pdf->Row(array($vv['pinming'],$vv['tks_fe'],$vv['tks_si'],$vv['tks_al'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JINSHU_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['strength'],$vv['xincengWeight'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}else{
					$pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}
				
				$js=$js+$vv['Fhjs'];
				$sl=$sl+$vv['Fhsl'];
			}
			
			//小计
			if(!empty($res)){
				
				$pdf->SetX(18);
				$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");		
				$pdf->Row(array('小计',$js,$sl,'','',''));  
				
			
			}
			$alljs=$alljs+$js;
			$allsl=$allsl+$sl;
        }
		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('总计',$alljs,$allsl,'','',''));  
		$pdf->Ln();		
	
		//UPdated by quanjw for meijiao start 2015/7/17  发货单 区分资源类型  
		
		/*$pdf->SetX(18);   
		$pdf->Cell(25,10,'品 名',1,0,'C');
		$pdf->Cell(25,10,'规 格',1,0,'C');
		$pdf->Cell(25,10,'材 质',1,0,'C');
        $pdf->Cell(25,10,'产地（钢厂）',1,0,'C');
        $pdf->Cell(20,10,'件 重',1,0,'C');
		$pdf->Cell(20,10,'件 数',1,0,'C');
        $pdf->Cell(20,10,'数 量',1,0,'C');
	    $pdf->Cell(25,10,'生产日期',1,0,'C');
		$pdf->Cell(25,10,'质保书',1,0,'C');
	    $pdf->Cell(25,10,'货号',1,0,'C');
		 $pdf->Ln();
   //alert($v.oid);

     $js=0;
	 $sl=0;
    foreach($pzzlist[$v['oid']] as $vv){

     
        $pdf->SetX(18);
        $pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));

        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
         $pdf->SetZeilenhoehe("5");
         $pdf->SetFont('simsun', '', 8); 
         $pdf->SetAligns("C");
		  
		 $A=$pdf->GetX();
         $pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

		  $js=$js+$vv['Shjs'];
	       $sl=$sl+$vv['Shsl'];
	  }

        $pdf->SetX(18);
		$pdf->SetWidths(array(100,20,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('小计','',$js,$sl,'','',''));  


	  $pdf->Ln();*/

	}

	$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(230);
		$pdf->Write(10,'总计：');
        $pdf->Write(10, '  '.$Allfh.'  吨');
		$pdf->Ln();

   
    $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'验收信息：'); 
		$pdf->Ln();
		
    foreach($zydetail as $v){//收货单信息
        
         
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
	    $pdf->Row(array('发货单号',$v['FhNum'],'收货日期',$v['ShDate'],'验收人',$v['Shlxr'])); 

        $pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,125)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");	
        $pdf->Row(array('联系电话',$v['Shphone'],'验收备注',$v['Shbz']));

       


        $pdf->Ln();
		
		//UPdated by quanjw for meijiao start 2015/7/17  收货单 区分资源类型  
		
		$zyll = $this->groupResourse($pzzlist[$v['oid']]);
		//file_put_contents("123.txt",print_r($zyll,true));
		$alljs = 0;
		$allsl = 0;
		foreach($zyll as $k => $res)
        {
			$pdf->SetX(18);
			//$pdf->Cell(25,12,'物料编码',1,0,'C');   
			
			//输出分类资源 长度一致
			if( $k == $GLOBALS["MEITAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'挥发分',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(18,10,'全硫分',1,0,'C');
			}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(19,10,'硫 分',1,0,'C');
				$pdf->Cell(18,10,'C S R',1,0,'C');
			}else if( $k == $GLOBALS["SHUINI_VID"]){
				$pdf->Cell(38,10,'品 名',1,0,'C');
				$pdf->Cell(37,10,'规 格',1,0,'C');
			}else if( $k == $GLOBALS["JINSHU_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'规 格',1,0,'C');
				$pdf->Cell(19,10,'强 度',1,0,'C');
				$pdf->Cell(18,10,'锌层重量',1,0,'C');
			}else if( $k == $GLOBALS["TKS_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'Fe%',1,0,'C');
				$pdf->Cell(19,10,'SiO2%',1,0,'C');
				$pdf->Cell(18,10,'AL2O3%',1,0,'C');
			}else{
				$pdf->Cell(25,10,'品 名',1,0,'C');
				$pdf->Cell(25,10,'规 格',1,0,'C');
				$pdf->Cell(25,10,'材 质',1,0,'C');
			}
			//Added by hezp for 17307 ended 2015/10/15
			
			$pdf->Cell(25,10,'产地（厂家）',1,0,'C');
			$pdf->Cell(20,10,'件 重',1,0,'C');
			$pdf->Cell(20,10,'件 数',1,0,'C');
			$pdf->Cell(20,10,'数 量',1,0,'C');
			$pdf->Cell(25,10,'生产日期',1,0,'C');
			$pdf->Cell(25,10,'质保书',1,0,'C');
			$pdf->Cell(25,10,'货号',1,0,'C');
			$pdf->Ln();
			
			//小计
			$js=0;
			$sl=0;
			foreach($res as $vv){
				

				$pdf->SetX(18);
				$pdf->SetLeftMargin(18);
				$pdf->SetZeilenhoehe("5");
				$pdf->SetFont('simsun', '', 8); 
				$pdf->SetAligns("C");


				$A=$pdf->GetX();
				$pdf->SetAligns("C");

				
				if( $k == $GLOBALS["MEITAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['cd'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['caizhi'],$vv['cd'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["SHUINI_VID"]){
					$pdf->SetWidths(array(38,37,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum']));
				}elseif( $k == $GLOBALS["JINSHU_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['strength'],$vv['xincengWeight'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["TKS_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['tks_fe'],$vv['tks_si'],$vv['tks_al'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}else{
					$pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}
				
				$js=$js+$vv['Shjs'];
				$sl=$sl+$vv['Shsl'];
			}
			
			//小计
			if(!empty($res)){
				
				$pdf->SetX(18);
				$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");		
				$pdf->Row(array('小计',$js,$sl,'','',''));  
				
			
			}
			$alljs=$alljs+$js;
			$allsl=$allsl+$sl;
        }
		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('总计',$alljs,$allsl,'','',''));  
		$pdf->Ln();		
	
		//UPdated by quanjw for meijiao end 2015/7/17  收货单 区分资源类型  
		
		
		

		/*$pdf->SetX(18);   
		$pdf->Cell(25,10,'品 名',1,0,'C');
		$pdf->Cell(25,10,'规 格',1,0,'C');
		$pdf->Cell(25,10,'材 质',1,0,'C');
        $pdf->Cell(25,10,'产地（钢厂）',1,0,'C');
        $pdf->Cell(20,10,'件 重',1,0,'C');
		$pdf->Cell(20,10,'件 数',1,0,'C');
        $pdf->Cell(20,10,'数 量',1,0,'C');
	    $pdf->Cell(25,10,'生产日期',1,0,'C');
		$pdf->Cell(25,10,'质保书',1,0,'C');
	    $pdf->Cell(25,10,'货号',1,0,'C');
		 $pdf->Ln();
   //alert($v.oid);

     $js=0;
	 $sl=0;
    foreach($pzzlist[$v['oid']] as $vv){

     
        $pdf->SetX(18);
        $pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));

        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
         $pdf->SetZeilenhoehe("5");
         $pdf->SetFont('simsun', '', 8); 
         $pdf->SetAligns("C");
		  
		 $A=$pdf->GetX();
         $pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

		  $js=$js+$vv['Shjs'];
	       $sl=$sl+$vv['Shsl'];
	  }

        $pdf->SetX(18);
		$pdf->SetWidths(array(100,20,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('小计','',$js,$sl,'','',''));  

	*/
	  $pdf->Ln();

	}

	$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(230);
		$pdf->Write(10,'总计：');
        $pdf->Write(10, '  '.$Allsh.'  吨');
		$pdf->Ln();
	   

		
	/*	



	
	 
	 $js=0;
	 $sl=0;
     foreach($zydetail as &$v){
		
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
		  $A=$pdf->GetX();
          $pdf->Row(array($v['pinming'],$v['caizhi'],$v['guige'],$v['Factory'],$v['Shjz'],$v['Shjs'],$v['Shsl'],$v['MfgDate'],$v['zbs'],$v['FhNum'])); 

		   $js=$js+$v['Shjs'];
	       $sl=$sl+$v['Shsl'];
            
       
     }
		
*/
		

		
		$pdf->Output($zy['OrderNo']. '.pdf', 'I'); //D or I

	}
   
   
   



//收货单

 public function shd($params)
    {   
          include("classfile.php");
          include("phpqrcode.php");

		$td = $this->_dao->getRow("select * from sm_contract_transaction_td where Tid = '". $params['id']."'");
		//仓库名
		$ck = $td['Ck'];
        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");

        
		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$params['id']."'");
		

        $buycp="";
		$sellcp="";
		//买家
		if($zy['SlType']=="1" || $zy['SlType']=="5" ){  //销售类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
		}else{//采购类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");	
			
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
		}
		
		


		
$Allsl=0;
$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order  where   Did='".$params['id']."'");

	
			foreach($zydetail as $val){
				//Updated by quanjw for meijiao start 2015/7/20
				//$pzzlist[$val['oid']] = $this->_dao->query("select *,sm_exc_order_detail.* from sm_exc_order_detail,sm_exc_dd where sm_exc_dd.ID=sm_exc_order_detail.Ddid and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
				$pzzlist[$val['oid']] = $this->_dao->query("select sm_exc_dd.*,sm_exc_order_detail.*,sm_exc_sales_details.Vid,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.tks_fe,sm_exc_sales_details.tks_si,sm_exc_sales_details.tks_al from sm_exc_order_detail,sm_exc_dd ,sm_exc_sales_details where sm_exc_dd.ID=sm_exc_order_detail.Ddid and sm_exc_sales_details.id = sm_exc_dd.sdid and  Oid = '".$val['oid']."' and  sm_exc_order_detail.Status=1 order by sm_exc_order_detail.ID ASC",0);
				//Updated by quanjw for meijiao start 2015/7/20
				
				$pzzlist2[$val['oid']] = $this->_dao->query("select *,sm_exc_order_detail.Status as Status2 from sm_exc_dd,sm_exc_order_detail where sm_exc_dd.ID=sm_exc_order_detail.Ddid and  Oid = '".$val['oid']."' order by sm_exc_order_detail.ID ASC",0);
				$Allsl = $Allsl+$val['Fhsl'];

			}


/*$zydetail=$this->_dao->query("select *,sm_exc_order.ID as oid from sm_exc_order,sm_exc_dd  where sm_exc_order.Did = sm_exc_dd.Dtid and sm_exc_order.Sdid = sm_exc_dd.sdid  and   Did='".$params['id']."'");
foreach($zydetail as $val){
	$pzzlist[$val['oid']] = $this->_dao->query("select * from sm_exc_order_detail where Oid = '".$val['oid']."' order by ID ASC",0);
	//echo "select * from sm_exc_order_detail where Oid = '".$val['oid']."' order by ID ASC";

	$Allsl = $Allsl+$val['Fhsl'];
}*/





//运输公司

$yscom = $this->_dao->query("select *  from sm_user_trans where Mid ='".$_SESSION['SYS_COMPANYID']."' and Status=1");



		if(empty($yscom)){
		$this->assign("nullyscom",1);
		}

		$yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");


		$ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '".$params['id']."'",0);

		$id = $ht['BID'];
		$MaxNum=$this->_dao->GetOne("select count(*) from sm_exc_order  where   Did='".$params['id']."'");
		//取出order表里面的订单数量
		$temp=0;//记数

		
		
		$pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,15);
	foreach($zydetail as $v)
	{
			
	    $temp++;//使用temp进行记数
        $pdf->SetFont('simhei','',12);
		//$pdf->SetX(40);
		//$pdf->Cell(0,10,'上海鞍钢国际贸易有限公司('.$ck.')钢材发货单',0,0,C); 
		//$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
		$pdf->Cell(0,10,$sellcp['ComName'].'   收货单',0,0,C); 
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'日期：');
        $pdf->Write(10, date("Y年m月d日"));
		$pdf->Ln();


		$pdf->SetX(18);
		$pdf->SetWidths(array(25,95,25,90)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("B");		
        $pdf->Row(array('采购方',$buycp['ComName'],'供应方',$sellcp['ComName']));
		$pdf->Ln();
      
          
        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'收货信息：'); 
		$pdf->Ln();

    //foreach($zydetail as $v){
        
         
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");
	    $pdf->Row(array('收货单号',$v['FhNum'],'收货日期',$v['FhDate'],'车牌号',$v['Carnum'])); 
		if($ht['Delivery']!=1){
				$pdf->SetX(18);
				$pdf->SetWidths(array(30,50,30,45,30,50)); // 设置每列的宽度
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");	
				$pdf->Row(array('联系人',$v['Fhlxr'],'联系电话',$v['Fhphone'],'运输公司',$yscom2[$v['Yscom']]));
		}
        //$pdf->Row(array('运输公司',$yscom2[$zydetail[0]['Yscom']],'联系人',$zydetail[0]['Fhlxr'],'联系电话',$zydetail[0]['Fhphone'])); 

		$pdf->SetX(18);
		$pdf->SetWidths(array(30,205)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('备注',$v['Fhbz']));

        $pdf->Ln();
		
		
		
		//UPdated by quanjw for meijiao start 2015/7/17  收货单 区分资源类型  
		
		$zyll = $this->groupResourse($pzzlist[$v['oid']]);
		//file_put_contents("123.txt",print_r($zyll,true));
		$alljs = 0;
		$allsl = 0;
		foreach($zyll as $k => $res)
        {
			$pdf->SetX(18);
			//$pdf->Cell(25,12,'物料编码',1,0,'C');   
			
			//输出分类资源 长度一致
			if( $k == $GLOBALS["MEITAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'挥发分',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(18,10,'全硫分',1,0,'C');
			}else if( $k == $GLOBALS["JIAOTAN_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'灰 分',1,0,'C');
				$pdf->Cell(19,10,'硫 分',1,0,'C');
				$pdf->Cell(18,10,'C S R',1,0,'C');
			}else if( $k == $GLOBALS["SHUINI_VID"]){
				$pdf->Cell(38,10,'品 名',1,0,'C');
				$pdf->Cell(37,10,'规 格',1,0,'C');
			}else if( $k == $GLOBALS["JINSHU_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'规 格',1,0,'C');
				$pdf->Cell(19,10,'强 度',1,0,'C');
				$pdf->Cell(18,10,'锌层重量',1,0,'C');
			}else if( $k == $GLOBALS["TKS_VID"]){
				$pdf->Cell(19,10,'品 名',1,0,'C');
				$pdf->Cell(19,10,'Fe%',1,0,'C');
				$pdf->Cell(19,10,'SiO2%',1,0,'C');
				$pdf->Cell(18,10,'AL2O3%',1,0,'C');
			}else{
				$pdf->Cell(25,10,'品 名',1,0,'C');
				$pdf->Cell(25,10,'规 格',1,0,'C');
				$pdf->Cell(25,10,'材 质',1,0,'C');
			}
			//Added by hezp for 17307 ended 2015/10/15
			
			
			$pdf->Cell(25,10,'产地（厂家）',1,0,'C');
			$pdf->Cell(20,10,'件 重',1,0,'C');
			$pdf->Cell(20,10,'件 数',1,0,'C');
			$pdf->Cell(20,10,'数 量',1,0,'C');
			$pdf->Cell(25,10,'生产日期',1,0,'C');
			$pdf->Cell(25,10,'质保书',1,0,'C');
			$pdf->Cell(25,10,'货号',1,0,'C');
			$pdf->Ln();
			
			//小计
			$js=0;
			$sl=0;
			foreach($res as $vv){
				

				$pdf->SetX(18);
				$pdf->SetLeftMargin(18);
				$pdf->SetZeilenhoehe("5");
				$pdf->SetFont('simsun', '', 8); 
				$pdf->SetAligns("C");


				$A=$pdf->GetX();
				$pdf->SetAligns("C");

				
				if( $k == $GLOBALS["MEITAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['cd'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['caizhi'],$vv['cd'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["SHUINI_VID"]){
					$pdf->SetWidths(array(38,37,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["JINSHU_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['guige'],$vv['strength'],$vv['xincengWeight'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}elseif( $k == $GLOBALS["TKS_VID"]){
					$pdf->SetWidths(array(19,19,19,18,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['tks_fe'],$vv['tks_si'],$vv['tks_al'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}else{
					$pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));
					$pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Shjz'],$vv['Shjs'],$vv['Shsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 
				}
				
				$js=$js+$vv['Shjs'];
				$sl=$sl+$vv['Shsl'];
			}
			
			//小计
			if(!empty($res)){
				
				$pdf->SetX(18);
				$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");		
				$pdf->Row(array('小计',$js,$sl,'','',''));  
				
			
			}
			$alljs=$alljs+$js;
			$allsl=$allsl+$sl;
        }
		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('总计',$alljs,$allsl,'','',''));  
		$pdf->Ln();		
	
		//UPdated by quanjw for meijiao end 2015/7/17  收货单 区分资源类型  

		/*$pdf->SetX(18);   
		$pdf->Cell(25,10,'品 名',1,0,'C');
		$pdf->Cell(25,10,'规 格',1,0,'C');
		$pdf->Cell(25,10,'材 质',1,0,'C');
        $pdf->Cell(25,10,'产地（钢厂）',1,0,'C');
        $pdf->Cell(20,10,'件 重',1,0,'C');
		$pdf->Cell(20,10,'件 数',1,0,'C');
        $pdf->Cell(20,10,'数 量',1,0,'C');
	    $pdf->Cell(25,10,'生产日期',1,0,'C');
		$pdf->Cell(25,10,'质保书',1,0,'C');
	    $pdf->Cell(25,10,'货号',1,0,'C');
		 $pdf->Ln();
   //alert($v.oid);

     $js=0;
	 $sl=0;

    foreach($pzzlist[$v['oid']] as $vv)
	 {

     
        $pdf->SetX(18);
        $pdf->SetWidths(array(25,25,25,25,20,20,20,25,25,25));

        $pdf->SetX(18);
		$pdf->SetLeftMargin(18);
         $pdf->SetZeilenhoehe("5");
         $pdf->SetFont('simsun', '', 8); 
         $pdf->SetAligns("C");
		  
		 $A=$pdf->GetX();
         $pdf->Row(array($vv['pinming'],$vv['caizhi'],$vv['guige'],$vv['Factory'],$vv['Fhjz'],$vv['Fhjs'],$vv['Fhsl'],$vv['MfgDate'],$vv['zbs'],$vv['FhNum'])); 

		  $js=$js+$vv['Fhjs'];
	       $sl=$sl+$vv['Fhsl'];
	  }

        $pdf->SetX(18);
		$pdf->SetWidths(array(120,20,20,25,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('小计',$js,$sl,'','',''));  
		*/


		if($temp<$MaxNum)			//控制增加页，避免有空白页
			$pdf->AddPage();
	}
		/*
		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(230);
		$pdf->Write(10,'总计：');
        $pdf->Write(10, '  '.$Allsl."  吨");
		$pdf->Ln();
			*/

       /* 

		


	 
     $js=0;
	 $sl=0;
     foreach($zydetail as &$v){
		 //alert("first.");
        //foreach($pzzlist as &$vv)
        //{   // alert("12");
		    //alert("hello");
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
		  $A=$pdf->GetX();
          $pdf->Row(array($v['pinming'],$v['caizhi'],$v['guige'],$v['Factory'],$v['Fhjz'],$v['Fhjs'],$v['Fhsl'],$v['MfgDate'],$v['zbs'],$v['FhNum'])); 
            
			
		   $js=$js+$v['Fhjs'];
	       $sl=$sl+$v['Fhsl'];
       // } 
     }
		$h = $pdf->GetY();
*/

		
		$pdf->Output($zy['OrderNo']. '.pdf', 'I'); //D or I

	
  

	}


//结算单

  public function finish($params)
    {   
          include("classfile.php");
          include("phpqrcode.php");


		  $td = $this->_dao->getRow("select * from sm_contract_transaction_td where Tid = '". $params['id']."'");

		//仓库名
		$ck = $td['Ck'];

	
        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");

        
		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$params['id']."'");
		

        $zydetail=$this->_dao->query("select sm_exc_dd.*,sm_exc_order.* ,sm_exc_order_detail.*  from sm_exc_order,sm_exc_dd,sm_exc_order_detail  where sm_exc_order.Did = sm_exc_dd.Dtid and sm_exc_order.Sdid = sm_exc_dd.sdid and sm_exc_order_detail.Oid=sm_exc_order.ID  and   Did='".$params['id']."'");

        $qtcost=$this->_dao->query("select * from sm_exc_order_cost  where Did='".$params['id']."'");
		$this->assign("qtcost",$qtcost);

        
        $buycp="";
        $sellcp="";
        if($zy['SlType']=="1" || $zy['SlType']=="5" ){  //销售类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
		}else{//采购类型订单
			$buycp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Did']."'");
			$sellcp=$this->_dao->getRow("select * from sys_company where ID = '".$zy['Mid']."'");	
			
			$sellcp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Mid']."' and  IsMain = 1");
			$buycp['ARealName'] = $this->_dao->getOne("select ARealName from sys_adminuser where ComID = '".$zy['Did']."' and  IsMain = 1");
		}

		//Updated by quanjw for meijiao start 2105/7/20
		//$zydetail=$this->_dao->query("select *,sm_exc_order_detail.ID as oid from sm_exc_order_detail,sm_exc_dd  where sm_exc_order_detail.Ddid = sm_exc_dd.ID  and   Dtid='".$params['id']."' and sm_exc_order_detail.Status=1");
		$zydetail=$this->_dao->query("select sm_exc_order_detail.*,sm_exc_dd.*,sm_exc_order_detail.ID as oid ,sm_exc_sales_details.Vid,sm_exc_sales_details.cd,sm_exc_sales_details.strength,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.tks_fe,sm_exc_sales_details.tks_si,sm_exc_sales_details.tks_al from sm_exc_order_detail,sm_exc_dd,sm_exc_sales_details  where sm_exc_order_detail.Ddid = sm_exc_dd.ID and sm_exc_sales_details.id = sm_exc_dd.sdid and   Dtid='".$params['id']."' and sm_exc_order_detail.Status=1");
		//Updated by quanjw for meijiao end 2105/7/20

		$JsTmoney=0;

		foreach($zydetail as &$tmp){
			if($tmp['Jsjg']=="0"){
				$JsTmoney = $JsTmoney + $tmp['XyPrice']* $tmp['Shsl'];
				$tmp['Jsmoney']=$tmp['XyPrice']* $tmp['Shsl'];
			}else{
				$JsTmoney = $JsTmoney + $tmp['Jsjg']* $tmp['Shsl'];		
				$tmp['Jsmoney']=$tmp['Jsjg']* $tmp['Shsl'];
			}
			
		}




//运输公司
//$yscom = $this->_dao->query("select *  from sm_user_trans where Mid ='".$_SESSION['SYS_COMPANYID']."' and Status=1");
 
 $yscom = $this->_dao->Aquery("select ID,TransName  from sm_user_trans ");


if(empty($yscom)){
$this->assign("nullyscom",1);
}


$htsf=$GLOBALS['HTSF'];

$yscom2 = $this->_dao->Aquery("select ID,TransName  from sm_user_trans where  Status=1");



$ht = $this->_dao->getRow("select * from sm_contract_transaction where BID = '".$params['id']."'",0);	
		
		$pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,15);

        $pdf->SetFont('simhei','',12);
		//$pdf->SetX(40);
		//$pdf->Cell(0,10,'上海鞍钢国际贸易有限公司('.$ck.')钢材结算单',0,0,C); 
		$pdf->Cell(0,10,$buycp['ComName'].'   结算单',0,0,C); 
		//$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'日期：');
        $pdf->Write(10, date("Y年m月d日"));
		$pdf->Ln();
		

        
		
        $pdf->SetX(18);
		$pdf->SetWidths(array(25,95,25,90)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("B");		
        $pdf->Row(array('采购方',$buycp['ComName'],'供应方',$sellcp['ComName']));
        

       // $pdf->SetX(18);
		//$pdf->Cell(235,7,'结算信息',1,0,'C');
		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		$pdf->Write(10,'结算信息：'); 
		
     
	    $pdf->Ln();
		
		//UPdated by quanjw for meijiao start 2015/7/17  结算单 区分资源类型
		$zyll = $this->groupResourse($zydetail);
		
		//总计
		$allsl=0;
		$alljssl=0;
		$allmoney=0;
		foreach($zyll as $k => $res)
        {
			$pdf->SetX(18);
			//$pdf->Cell(25,12,'物料编码',1,0,'C');   
			
			$pdf->Cell(25,12,'资源编号',1,0,'C');   
			//输出分类资源 长度一致
			if( $k == $GLOBALS["MEITAN_VID"]){
				$pdf->Cell(19,12,'品 名',1,0,'C');
				$pdf->Cell(19,12,'挥发分',1,0,'C');
				$pdf->Cell(19,12,'灰 分',1,0,'C');
				$pdf->Cell(18,12,'全硫分',1,0,'C');
			}else if( $k == $GLOBALS["JIAOTAN_VID"]){
				$pdf->Cell(19,12,'品 名',1,0,'C');
				$pdf->Cell(19,12,'灰 分',1,0,'C');
				$pdf->Cell(19,12,'硫 分',1,0,'C');
				$pdf->Cell(18,12,'C S R',1,0,'C');
			}else if( $k == $GLOBALS["SHUINI_VID"]){
				$pdf->Cell(38,12,'品 名',1,0,'C');
				$pdf->Cell(37,12,'规 格',1,0,'C');
			}else if( $k == $GLOBALS["JINSHU_VID"]){
				$pdf->Cell(19,12,'品 名',1,0,'C');
				$pdf->Cell(19,12,'规 格',1,0,'C');
				$pdf->Cell(19,12,'强 度',1,0,'C');
				$pdf->Cell(18,12,'锌层重量',1,0,'C');
			}else if( $k == $GLOBALS["TKS_VID"]){
				$pdf->Cell(19,12,'品 名',1,0,'C');
				$pdf->Cell(19,12,'Fe%',1,0,'C');
				$pdf->Cell(19,12,'SiO2%',1,0,'C');
				$pdf->Cell(18,12,'AL2O3%',1,0,'C');
			}else{
				$pdf->Cell(25,12,'品 名',1,0,'C');
				$pdf->Cell(25,12,'规 格',1,0,'C');
				$pdf->Cell(25,12,'材 质',1,0,'C');
			}
			$pdf->Cell(25,12,'产地（厂家）',1,0,'C');
			$pdf->Cell(20,12,'数 量',1,0,'C');
			$pdf->Cell(20,12,'单 价',1,0,'C');
			$pdf->Cell(20,12,'结算数量',1,0,'C');
		   
			$pdf->Cell(25,12,'结算价格',1,0,'C');
			$pdf->Cell(25,12,'金 额(元)',1,0,'C');
			
			$pdf->Ln();
			
			//小计
			$sl=0;
			$jssl=0;
			$money=0;
			foreach($res as $v){
				//输出资源详细信息$w1=0;
				
				//$pdf->SetWidths(array(41,30,25,17,17,17,17,18,26,27)); // 设置每列的宽度
				$pdf->SetX(18);
				$pdf->SetLeftMargin(18);
				$pdf->SetZeilenhoehe("5");
				$pdf->SetFont('simsun', '', 8); 
				$pdf->SetAligns("C","C");
				$A=$pdf->GetX();
				
				if( $k == $GLOBALS["MEITAN_VID"]){
					$pdf->SetWidths(array(25,19,19,19,18,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['caizhi'],$v['guige'],$v['cd'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}elseif( $k == $GLOBALS["JIAOTAN_VID"]){
					$pdf->SetWidths(array(25,19,19,19,18,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['guige'],$v['caizhi'],$v['cd'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}elseif( $k == $GLOBALS["SHUINI_VID"]){
					$pdf->SetWidths(array(25,38,37,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['guige'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}elseif( $k == $GLOBALS["JINSHU_VID"]){
					$pdf->SetWidths(array(25,19,19,19,18,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['guige'],$v['strength'],$v['xincengWeight'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}elseif( $k == $GLOBALS["TKS_VID"]){
					$pdf->SetWidths(array(25,19,19,19,18,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['tks_fe'],$v['tks_si'],$v['tks_al'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}else{
					$pdf->SetWidths(array(25,25,25,25,25,20,20,20,25,25)); // 设置每列的宽度
					$pdf->Row(array($v['Sid'],$v['pinming'],$v['caizhi'],$v['guige'],$v['Factory'],$v['Shsl'],
					number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
				}
				
				$sl=$sl+$v['Shsl'];
				$jssl=$jssl+$v['Shsl'];
				$money=$money+$v['Jsmoney'];
			}
			
			//小计
			if(!empty($res)){
				
				$h = $pdf->GetY();

				$pdf->SetX(18);
				$pdf->SetWidths(array(125,20,20,20,25,25)); // 设置每列的宽度  收货人信息
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', 'B', 9); 
				$pdf->SetAligns("C");		
				$pdf->Row(array('小计',$sl,'',$jssl,'',number_format($money,2))); 
				$pdf->Ln(); 	
				
			
			}
			//总计
			$allsl=$allsl+$sl;
            $alljssl=$alljssl+$jssl;
			$allmoney=$allmoney+$money;
			
        }

		$h = $pdf->GetY();

		$pdf->SetX(18);
		$pdf->SetWidths(array(125,20,20,20,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计',$allsl,'',$alljssl,'',number_format($allmoney,2))); 	
		$pdf->Ln(); 		
		//Updated by quanjw for meijiao start 2015/
		
		
		
    
		/* $pdf->SetX(18);
		$pdf->Cell(25,12,'资源编号',1,0,'C');   
		$pdf->Cell(25,12,'品 名',1,0,'C');
		$pdf->Cell(25,12,'规 格',1,0,'C');
		$pdf->Cell(25,12,'材 质',1,0,'C');
        $pdf->Cell(25,12,'产地（钢厂）',1,0,'C');
        $pdf->Cell(20,12,'数 量',1,0,'C');
		$pdf->Cell(20,12,'单 价',1,0,'C');
        $pdf->Cell(20,12,'结算数量',1,0,'C');
	   
		$pdf->Cell(25,12,'结算价格',1,0,'C');
	    $pdf->Cell(25,12,'金 额(元)',1,0,'C');
		
		$pdf->Ln();

		//$pdf->SetX(18);
        $pdf->SetWidths(array(25,25,25,25,25,20,20,20,25,25)); // 设置每列的宽度
    
     //alert("out ");

	 $sl=0;
	 $jssl=0;
	 $money=0;
     foreach($zydetail as &$v){
	
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
		    $A=$pdf->GetX();
           $pdf->Row(array($v['Sid'],$v['pinming'],$v['caizhi'],$v['guige'],$v['Factory'],$v['Shsl'],
		   number_format($v['XyPrice'],2),$v['Shsl'],number_format($v['Jsjg'],2),number_format($v['Jsmoney'],2))); 
            //alert("end");
            $sl=$sl+$v['Shsl'];
            $jssl=$jssl+$v['Shsl'];
			$money=$money+$v['Jsmoney'];

       // } 
     }
		$h = $pdf->GetY();

 

		$pdf->SetX(18);
		$pdf->SetWidths(array(125,20,20,20,25,25)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计',$sl,'',$jssl,'',number_format($money,2))); 
		 */
 	 if($ht['Delivery']=="2" && $ht['ysfy']=="2"){

		$pdf->SetFont('simsun', 'B', 10); //设置字体样式
        $pdf->SetX(18);
		
		$pdf->Write(10,'其他费用：'); 
     
	    $pdf->Ln();
		$pdf->SetX(18);
        $pdf->Cell(40,12,'项 目',1,0,'C');   
		$pdf->Cell(40,12,'物流公司',1,0,'C');
		$pdf->Cell(40,12,'数量(吨)',1,0,'C');
		$pdf->Cell(40,12,'单价(元/吨)',1,0,'C');
        $pdf->Cell(40,12,'金额(元)',1,0,'C');
        $pdf->Cell(35,12,'备 注',1,0,'C');
	
		$pdf->Ln();

        $pdf->SetWidths(array(40,40,40,40,40,35)); // 设置每列的宽度
 
        $sl=0;
        foreach($qtcost as &$v){
	
			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetAligns("C");
		   $A=$pdf->GetX();
           $pdf->Row(array('运费',$yscom[$v['YsComID']],$v['Yssl'],$v['Ysdj'],$v['Ysmoney'],$v['Ysbz'])); 
            //alert("end");
			$sl=$sl+$v['Yssl'];
        
        }
        
		
  
        $pdf->SetX(18);
		$pdf->SetWidths(array(80,40,40,40,35)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计',$sl,'',$zy['YsTmoney'],''));

        
   
        $pdf->SetX(18);
		$pdf->SetWidths(array(40,195)); // 设置每列的宽度  收货人信息
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('费用承担方',$htsf[$ht['ysfy']]));
	 }
		
		$pdf->Output($zy['OrderNo']. '.pdf', 'I'); //D or I

	}

    //鞍钢合同模板  提货单
    public function tidan($params)
    {
        $hth = $params['hth'];
        //$this->setvars();
        include("classfile.php");
        include("phpqrcode.php");
        
        $pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        
        //$td = $this->_dao->getRow("select * from sm_contract_transaction_td where Tid = '".$hth."'");
        $tds = $this->_dao->query("select * from sm_contract_transaction_td where Tid = '".$hth."'");
        foreach($tds as $td){ //仓库循环开始 added by quanjw at 2015/7/27
            //仓库名
            $ck = $td['Ck'];

        
            $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");
            // 资源信息

            // Updated for thd by Zhu Dahua started  2015/09/22

            //$zydetail = $this->_dao->query("select *,(BuyQuantity * SalesPrice) as totaljg from sm_contract_transaction_detail where TDID = '" . $td['ID'] . "' and TDID !='' ");

            $orderDid = $this->_dao->getOne("SELECT BID FROM `sm_contract_transaction` WHERE ID =".$hth);

            $zydetail = $this->_dao->getTDdetailsBydtid($orderDid);

            // Updated for thd by Zhu Dahua ended  2015/09/22

            /*foreach($zydetail as &$tmp){
                $aa = $this->_dao->getRow("select * from  sm_exc_sales  where ID='".$tmp['Sid']."'");
                $tmp['ResourceNum']=$aa['ResourceNum'];
            }*/
            //总价格
            $totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail GROUP BY TDID HAVING TDID = '" . $hth . "'");

            //总数量
            $totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail GROUP BY TDID HAVING TDID = '" . $td['ID'] . "'");

            //品种
            $pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");

            //$new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
            // 买方公司名称
            $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
            //买方公司联系人
            $buyuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$buycomp['ID']."' and IsMain='1' limit 1 ");
            
            // 卖方公司名称
            $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
            //卖方公司联系人
            $saleuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$salecomp['ID']."' and IsMain='1' limit 1 ");

            // 银行信息
            $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
            $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

            
            
            
            $pdf->AddPage();
            $pdf->SetAutoPageBreak(true,15);

            $pdf->SetFont('simhei','',12);
            //$pdf->SetX(40);
            $pdf->Cell(0,10,$buycomp['ComName'].'   提货单',0,0,C); 
            //$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
            $pdf->Ln();

            $pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(18);
            $pdf->Write(10,'日期：');
            $pdf->Write(10, date("Y年m月d日"));
            $pdf->Ln();
            
            
            //UPdated by quanjw for meijiao start 2015/7/17  提货单 区分资源类型
            $zyll = $this->groupResourse($zydetail);
            //总计
            $allw1 = 0;
            $allw2 = 0;
            $allTmoney=0;
            foreach($zyll as $k => $res)
            {
                $pdf->SetX(18);
                //$pdf->Cell(25,12,'物料编码',1,0,'C');   
                
                //输出分类资源 长度一致
                if( $k == $GLOBALS["MEITAN_VID"]){
                    $pdf->Cell(24,12,'品 名',1,0,'C');
                    $pdf->Cell(24,12,'挥发分',1,0,'C');
                    $pdf->Cell(24,12,'灰 分',1,0,'C');
                    $pdf->Cell(24,12,'全硫分',1,0,'C');
                }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                    $pdf->Cell(24,12,'品 名',1,0,'C');
                    $pdf->Cell(24,12,'灰 分',1,0,'C');
                    $pdf->Cell(24,12,'硫 分',1,0,'C');
                    $pdf->Cell(24,12,'C S R',1,0,'C');
                }elseif( $k == $GLOBALS["SHUINI_VID"]){
                    $pdf->Cell(48,12,'品 名',1,0,'C');
                    $pdf->Cell(48,12,'规 格',1,0,'C');
                }elseif( $k == $GLOBALS["JINSHU_VID"]){
                    $pdf->Cell(24,12,'品 名',1,0,'C');
                    $pdf->Cell(24,12,'规 格',1,0,'C');
                    $pdf->Cell(24,12,'强 度',1,0,'C');
                    $pdf->Cell(24,12,'锌层重量',1,0,'C');
                }elseif( $k == $GLOBALS["TKS_VID"]){
                    $pdf->Cell(24,12,'品 名',1,0,'C');
                    $pdf->Cell(24,12,'Fe%',1,0,'C');
                    $pdf->Cell(24,12,'SiO2%',1,0,'C');
                    $pdf->Cell(24,12,'AL2O3%',1,0,'C');
                }else{
                    $pdf->Cell(41,12,'品 名',1,0,'C');
                    $pdf->Cell(30,12,'规 格',1,0,'C');
                    $pdf->Cell(25,12,'材 质',1,0,'C');
                }
                $w = $pdf->GetX();
                $h = $pdf->GetY();
                $pdf->Cell(34,6,'预出库',1,0,'C');
                
                $pdf->Ln();
                $pdf->SetX($w);
                $pdf->Cell(17,6,'件数',1,0,'C');
                $pdf->Cell(17,6,'重量',1,0,'C');
                
                $pdf->SetXY($w+34,$h);
                $pdf->Cell(34,6,'仓库实际出库',1,0,'C');
                $pdf->Ln();
                $pdf->SetX($w+34);
                $pdf->Cell(17,6,'件数',1,0,'C');
                $pdf->Cell(17,6,'重量',1,0,'C');
                $pdf->SetXY($w+68,$h);
                $pdf->Cell(18,12,'销售价格',1,0,'C');
                $pdf->SetXY($w+86,$h);
                $pdf->Cell(26,12,'销售金额',1,0,'C');
                $pdf->SetXY($w+112,$h);
                $pdf->Cell(27,12,'说 明',1,0,'C');
                $pdf->Ln();
                
                //小计
                $w1=0;
                $w2=0;
                $Tmoney=0;
                foreach($res as $v){
                    //输出资源详细信息$w1=0;
                    
                    //$pdf->SetWidths(array(41,30,25,17,17,17,17,18,26,27)); // 设置每列的宽度
                    $pdf->SetX(18);
                    $pdf->SetLeftMargin(18);
                    $pdf->SetZeilenhoehe("5");
                    $pdf->SetFont('simsun', '', 8); 
                    $pdf->SetAligns("C","C");
                    $A=$pdf->GetX();
                    
                    // Updated for thd by Zhu Dahua started  2015/09/22

                    /*if( $k == $GLOBALS["MEITAN_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['MaterialCode'],$v['SpecCode'],$v['cd'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['cd'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["SHUINI_VID"]){
                        $pdf->SetWidths(array(48,48,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["JINSHU_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['strength'],$v['xincengWeight'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                    }else{
                        $pdf->SetWidths(array(41,30,25,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                    }
                    
                    $w1=$w1+$v['BuyQuantity'];
                    $w2=$w2+$v['BuyQuantity'];
                    $Tmoney=$Tmoney+$v['totaljg'];*/

                    if( $k == $GLOBALS["MEITAN_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['MaterialCode'],$v['SpecCode'],$v['cd'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["JIAOTAN_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['cd'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["SHUINI_VID"]){
                        $pdf->SetWidths(array(48,48,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["JINSHU_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['strength'],$v['xincengWeight'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }elseif( $k == $GLOBALS["TKS_VID"]){
                        $pdf->SetWidths(array(24,24,24,24,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['tks_fe'],$v['tks_si'],$v['tks_al'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }else{
                        $pdf->SetWidths(array(41,30,25,17,17,17,17,18,26,27)); // 设置每列的宽度
                        $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['fhsl'],number_format($v['jsjg'],2),number_format($v['fhtotaljg'],2),'')); 
                    }
                    
                    $w1=$w1+$v['BuyQuantity'];
                    $w2=$w2+$v['fhsl'];
                    $Tmoney=$Tmoney+$v['fhtotaljg'];
                    // Updated for thd by Zhu Dahua ended  2015/09/22

                }
                
                //小计
                if(!empty($res)){
                    
                    $h = $pdf->GetY();

                    $pdf->SetX(18);
                    $pdf->SetWidths(array(96,17,17,17,17,18,26,27)); // 设置每列的宽度
                    $pdf->SetZeilenhoehe("6");
                    $pdf->SetFont('simsun', 'B', 9); 
                    $pdf->SetAligns("C");
                    $pdf->Row(array('小计','',$w1,'',$w2,'',number_format($Tmoney,2),'')); 
                    $pdf->Ln();
                    
                
                }
                //总计
                $allw1 = $allw1+$w1;
                $allw2 = $allw2+$w2;
                $allTmoney = $allTmoney+$Tmoney;
                
            }
            $h = $pdf->GetY();

            $pdf->SetX(18);
            $pdf->SetWidths(array(96,17,17,17,17,18,26,27)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('总计','',$allw1,'',$allw2,'',number_format($allTmoney,2),'')); 	
            $pdf->Ln();
            
            //仓库名
            $pdf->SetX(18);
            $pdf->SetWidths(array(25,210)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('仓库',$td['Ck']));

            //Updated by quanjw for meijiao start 2015/
            
            
            /*
            $pdf->SetX(18);
            //$pdf->Cell(25,12,'物料编码',1,0,'C');   
            $pdf->Cell(41,12,'品 名',1,0,'C');
            $pdf->Cell(30,12,'规 格',1,0,'C');
            $pdf->Cell(25,12,'材 质',1,0,'C');
            $w = $pdf->GetX();
            $h = $pdf->GetY();
            $pdf->Cell(34,6,'预出库',1,0,'C');
            
            $pdf->Ln();
            $pdf->SetX($w);
            $pdf->Cell(17,6,'件数',1,0,'C');
            $pdf->Cell(17,6,'重量',1,0,'C');
            
            $pdf->SetXY($w+34,$h);
            $pdf->Cell(34,6,'仓库实际出库',1,0,'C');
            $pdf->Ln();
            $pdf->SetX($w+34);
            $pdf->Cell(17,6,'件数',1,0,'C');
            $pdf->Cell(17,6,'重量',1,0,'C');
            $pdf->SetXY($w+68,$h);
            $pdf->Cell(18,12,'销售价格',1,0,'C');
            $pdf->SetXY($w+86,$h);
            $pdf->Cell(26,12,'销售金额',1,0,'C');
            $pdf->SetXY($w+112,$h);
            $pdf->Cell(27,12,'说 明',1,0,'C');
            $pdf->Ln();

            //$pdf->SetX(18);
            $pdf->SetWidths(array(41,30,25,17,17,17,17,18,26,27)); // 设置每列的宽度
            
            $w1=0;
            $w2=0;
            $Tmoney=0;
            
            foreach($zydetail as $v)
            { 
                    
                $pdf->SetX(18);
                $pdf->SetLeftMargin(18);
                $pdf->SetZeilenhoehe("5");
                $pdf->SetFont('simsun', '', 8); 
                $pdf->SetAligns("C","C");
                $A=$pdf->GetX();
                $pdf->Row(array($v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['PerNumber'],$v['BuyQuantity'],$v['PerNumber'],$v['BuyQuantity'],number_format($v['SalesPrice'],2),number_format($v['totaljg'],2),'')); 
                $w1=$w1+$v['BuyQuantity'];
                $w2=$w2+$v['BuyQuantity'];
                $Tmoney=$Tmoney+$v['totaljg'];
                
            } 
            
            $h = $pdf->GetY();

            $pdf->SetX(18);
            $pdf->SetWidths(array(96,17,17,17,17,18,26,27)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('合计','',$w1,'',$w2,'',number_format($Tmoney,2),'')); 
            */
            

            $pdf->SetX(18);
            $pdf->SetWidths(array(25,139,17,54)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('购货单位',$buycomp['ComName'],'邮  编',$contact['ConsigneePostCode'])); 

            $pdf->SetX(18);
            $pdf->SetWidths(array(25,88,17,34,17,54)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('办公地址',$contact['ConsigneeAddress'],'联系人',$contact['ConsigneeMan'],'联系电话',$contact['ConsigneePhone'])); 

            $pdf->SetX(18);
            $pdf->SetWidths(array(25,122,17,71)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('税票地址','','开户行',$buycomp['BankType'])); 

            $pdf->SetX(18);
            $pdf->SetWidths(array(25,59,25,50,17,59)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("C");
            $pdf->Row(array('税    号',$buycomp['TaxNo'],'税票电话','','帐  号',$buybank['MBR_SPE_ACCT_NO']));

            $pdf->SetX(18);
            $pdf->SetWidths(array(82,82,71)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("L");
			$pdf->Row(array('供应方经办人签字（盖章）：



					   日期：		
	','仓库经办人签字（盖章）：



						   日期：				
	','购货单位代表签字：



					日期：		
	'));



        }//仓库循环结束
        // for($i=0;$i<5;$i++){
           
        //     $pdf->Image("img/xietest51.png", 10, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 60, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 110, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 160, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 210, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 260, 50*$i, 0 ,0);
        //     $pdf->Image("img/xietest51.png", 310, 50*$i, 0 ,0);
        // }

        $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
    }

	//add by zfy started 2018/8/23马钢提货单
    public function mg_tihuodan($params)
    {
		$ddid = $params['ddid'];
        $hth = $params['hth'];
        //$this->setvars();
        include("classfile.php");
        include("phpqrcode.php");
        
        $pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        
        $tds = $this->_dao->query("select * from sm_contract_transaction_td where Tid = '".$hth."'");
		//echo "<pre>";print_R($tds);
        foreach($tds as $td){ //仓库循环开始 added by quanjw at 2015/7/27
            //仓库名
            $ck = $td['Ck'];
			//提单号
            //$tidanNO = $td['TdNo'];
			//创建日期
            $CreateDate = $td['CreateDate'];

        
            $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $td['Tid'] . "' limit 1");
            // 资源信息

            $orderDid = $this->_dao->getOne("SELECT BID FROM `sm_contract_transaction` WHERE ID =".$hth);
            

            $zydetail = $this->_dao->getTDdetailsBydtidandjhck($orderDid,$ck);
            $dd_tag = $this->_dao->getRow("SELECT mg_dd,mg_tidan FROM sm_exc_dd_tag WHERE ID =".$orderDid);
            file_put_contents("/tmp/mg_tihuodan",print_r($orderDid,true)."<br>",FILE_APPEND);
            $orderDid = $dd_tag['mg_dd'];
            $tidanNO = $dd_tag['mg_tidan'];
            file_put_contents("/tmp/mg_tihuodan",print_r($tidanNO,true)."<br>",FILE_APPEND);

            //总价格
            $totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail GROUP BY TDID HAVING TDID = '" . $hth . "'");

            //总数量
            $totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail GROUP BY TDID HAVING TDID = '" . $td['ID'] . "'");

            //品种
            $pzs2 = $this->_dao->AQuery("SELECT VarietyCode, VarietyName FROM sm_base_variety Where 1=1 ORDER BY VarietyType");

            //$new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
            // 买方公司名称
            $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
            //买方公司联系人
            $buyuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$buycomp['ID']."' and IsMain='1' limit 1 ");
			//echo "<pre>";print_R($buyuser['ARealName']);
            // 卖方公司名称
            $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
            //卖方公司联系人
            $saleuser = $this->_dao->getRow("SELECT * FROM sys_adminuser WHERE ComID='".$salecomp['ID']."' and IsMain='1' limit 1 ");           
            
            
            $pdf->AddPage();
            $pdf->SetAutoPageBreak(true,15);
 
			$pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(230);
            $pdf->Write(0,'质量记录');
            $pdf->Ln();

            $pdf->SetFont('simhei','',12);
            if($buycomp['mg_id']!=null && $buycomp['mg_id']!=""){
            $pdf->Cell(0,0,'马鞍山钢铁股份有限公司',0,0,C); 
            }else{
            $pdf->Cell(0,0,'钢城钢之家电子商务有限公司',0,0,C); 
            }
			$pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(230);
            $pdf->Write(10,'编号：');
            $pdf->Ln();

            $pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(18);
            $pdf->Write(10,'提货单号：');
            $pdf->Write(10,$tidanNO);

			$pdf->SetX(230);
            $pdf->Write(0,'开票日期：');
            $pdf->Write(0,$CreateDate);
            $pdf->Ln();

			$pdf->SetFont('simhei','',20);
            $pdf->Cell(0,10,'产品提货单',0,0,C); 
			$pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(18);
            $pdf->Write(0,'收货单位：');
            $pdf->Write(0,$buycomp['ComName']);
			$pdf->SetX(230);
            $pdf->Write(10,'库存地点：');
            $pdf->Write(10,$ck);
            $pdf->Ln();
			$pdf->SetFont('simsun', 'B', 10); //设置字体样式

            
            //echo "<pre>";print_R($zydetail);
            //$zyll = $this->groupResourse($zydetail);
			//echo "<pre>";print_R($zyll);
            //总计
            $allw1 = 0;
            $allw2 = 0;
            $allTmoney=0;
            $pdf->SetX(18);
            $pdf->Cell(20,12,'序号',1,0,'C');
            $pdf->Cell(25,12,'订单号',1,0,'C');
            $pdf->Cell(15,12,'项目',1,0,'C');
            $pdf->Cell(20,12,'品名',1,0,'C');
            $pdf->Cell(25,12,'物料号',1,0,'C');
            $pdf->Cell(25,12,'规  格',1,0,'C');
                
            $w = $pdf->GetX();
            $h = $pdf->GetY();
            $pdf->Cell(50,6,'数量(吨)',1,0,'C');
                
            $pdf->Ln();
            $pdf->SetX($w);
            $pdf->Cell(25,6,'应发',1,0,'C');
            $pdf->Cell(25,6,'实发',1,0,'C');
                
            $pdf->SetXY($w+50,$h);
            $pdf->Cell(40,12,'批次号',1,0,'C');
            $pdf->SetXY($w+90,$h);
            $pdf->Cell(40,12,'备注',1,0,'C');
            $pdf->Ln();
                
            //小计
            $w1=0;
            $w2=0;
            $Tmoney=0;
			$noid = 0;
            foreach($zydetail as $v){
				$noid++;
                //输出资源详细信息$w1=0;
                $pdf->SetX(18);
                $pdf->SetLeftMargin(18);
                $pdf->SetZeilenhoehe("10");
                $pdf->SetFont('simsun', '', 8); 
                $pdf->SetAligns("C","C");
                $A=$pdf->GetX();

                    
                $pdf->SetWidths(array(20,25,15,20,25,25,25,25,40,40)); // 设置每列的宽度
                $pdf->Row(array($noid,$orderDid,"",$v['VarietyName'],$v['wuliaoNo'],$v['SpecCode'],$v['fhsl'],'',"","")); 
                    
                    
                //$w1=$w1+$v['BuyQuantity'];
                $w2=$w2+$v['fhsl'];
                //$Tmoney=$Tmoney+$v['fhtotaljg'];

                }
                
                    
                $h = $pdf->GetY();

                $pdf->SetX(18);
                $pdf->SetWidths(array(105,155)); // 设置每列的宽度
                $pdf->SetZeilenhoehe("10");
                $pdf->SetFont('simsun', 'B', 9); 
                $pdf->SetAligns("C");
                $pdf->Row(array('应发吨位合计(大写)：',$this->cnWeight($w2))); 
                $pdf->Ln();
                    
                
                //总计
                $allw1 = $allw1+$w1;
                $allw2 = $allw2+$w2;
                $allTmoney = $allTmoney+$Tmoney;
                
            
            
            $pdf->SetX(18);
            
            $pdf->SetWidths(array(160,20,80)); // 设置每列的宽度
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simsun', 'B', 9); 
            $pdf->SetAligns("L");
            
			$pdf->Row(array('  说明：1、按品名、物料号、规格、数量和批次发货，不得错发、超发。
        2、从开单之日起，限   日内提货，逾期或隔月此单无效。
        3、加盖产品自提单专用章后，方能发货，提单中打印内容一经涂改，此单无效。','             说      明',''));

            $pdf->SetFont('simsun', 'B', 10); //设置字体样式
            $pdf->SetX(18);
            $pdf->Write(10,'制单：');
			$pdf->SetX(180);
            $pdf->Write(10,'提货人：');
            $pdf->Write(10,$buyuser['ARealName']);
            $pdf->SetX(230);
            $pdf->Write(10,'发货人：');

            $pdf->Ln();
        }

        $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
    }
	//重量数字转换成大写
	function cnWeight($ns) { 
		// updated by libing for num2cny start in 2016/01/06
		require_once(APP_DIR."/pdf/Ext_Num2Cny.php");
		$cny = Ext_Num2Cny::ParseNumberWeight($ns);
		return $cny;
	}
	//add by zfy ended 2018/8/23马钢提货单

	//人民币数字转换成大写
	function cny($ns) { 
		// updated by libing for num2cny start in 2016/01/06
		require_once(APP_DIR."/pdf/Ext_Num2Cny.php");
		$cny = Ext_Num2Cny::ParseNumber($ns);
		return $cny;
		// updated by libing for num2cny end in 2016/01/06
		
		/*
		static $cnums=array("零","壹","贰","叁","肆","伍","陆","柒","捌","玖"), 
		$cnyunits=array("圆","角","分"), 
		$grees=array("拾","佰","仟","万","拾","佰","仟","亿"); 
		list($ns1,$ns2)=explode(".",$ns,2); 
		$ns2=array_filter(array($ns2[1],$ns2[0])); 
		$ret=array_merge($ns2,array(implode("",$this->_cny_map_unit(str_split($ns1),$grees)),"")); 
		$ret=implode("",array_reverse($this->_cny_map_unit($ret,$cnyunits))); 
		return str_replace(array_keys($cnums),$cnums,$ret);
		*/ 
	}
	function _cny_map_unit($list,$units) { 
		$ul=count($units); 
		$xs=array(); 
		foreach (array_reverse($list) as $x) { 
		$l=count($xs); 
		if ($x!="0" || !($l%4)) $n=($x=='0'?'':$x).($units[($l-1)%$ul]); 
		else $n=is_numeric($xs[0][0])?$x:''; 
		array_unshift($xs,$n); 
		} 
		return $xs; 
	}


	public function dcpdf($params){
		include("classfile.php");
        include("phpqrcode.php");
		$this->assign("params",$params);

		if($params['cname']!=''){
			 $where .= " AND ((ComName LIKE BINARY '%".$params['cname']."%')  or (ComNameShort LIKE BINARY '%".$params['cname']."%') ) ";
		}

		if ($params["Bid"] != "") {
			$where .= " and Bid =  '".$params["Bid"]."' ";
		}
		if ($params["OrderNO"] != "") {
			$where .= " and sc.OrderNO LIKE  '%".$params["OrderNO"]."%' ";
		}
		if ($params["JyDate"] != "") {
			$where .= " and JyDate >=  '".$params["JyDate"]."' ";
		}
		if ($params["JyDate2"] != "") {
			$where .= " and JyDate <= '".$params["JyDate2"]."' ";
		}

		
		if($params['cname']!=''){
			$list = $this->_dao->query("select sc.* from sm_info_cost as sc left join sm_exc_buy as sa  on sc.Bid=sa.ID left join sys_company as sy on    
		( ((sc.Mid_Shipper=sy.ID or sc.Mid_Consignee=sy.ID) and sc.PayMid=0 ) or (((sc.Mid_Shipper=sc.PayMid and sc.PayMid=sy.ID)or (sc.Mid_Consignee=sc.PayMid and sc.PayMid=sy.ID) ) and sc.PayMid!=0 ) ) where 1  $where  ORDER BY sc.ID  DESC ");
		}else{
			$list = $this->_dao->query("select sc.* from sm_info_cost as sc left join sm_exc_buy as sa  on sc.Bid=sa.ID left join sys_company as sy on ((sc.PayMid=sy.ID) and ((sc.Mid_Shipper=sy.ID) or (sc.Mid_Consignee=sy.ID)) ) where 1  $where  ORDER BY sc.ID  DESC ");
		}

		$paystatus=array("0"=>"未支付","1"=>"已支付");
		$SXFLX=array("1"=>"按笔计费","2"=>"按吨计费","3"=>"按百分比计费");

		if($params['cname']!=''){
			$money= $this->_dao->getRow("select SUM(sc.TotalMoney) as total,SUM(sc.Cost) as cost,SUM(sc.YhCost) as yhcost from sm_info_cost as sc left join sm_exc_buy as sa  on sc.Bid=sa.ID  left join sys_company as sy on ( ((sc.Mid_Shipper=sy.ID or sc.Mid_Consignee=sy.ID) and sc.PayMid=0 ) or (((sc.Mid_Shipper=sc.PayMid and sc.PayMid=sy.ID)or (sc.Mid_Consignee=sc.PayMid and sc.PayMid=sy.ID) ) and sc.PayMid!=0 ) )  where 1  $where");
		}else{
			$money= $this->_dao->getRow("select SUM(sc.TotalMoney) as total,SUM(sc.Cost) as cost,SUM(sc.YhCost) as yhcost from sm_info_cost as sc left join sm_exc_buy as sa  on sc.Bid=sa.ID  left join sys_company as sy on ((sc.PayMid=sy.ID) and ((sc.Mid_Shipper=sy.ID) or (sc.Mid_Consignee=sy.ID))) where 1  $where");
		}


		$pdf = new PDF_Chinese("L","mm", "A4");

        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,15);

		$pdf->SetFont('simhei','',12);
		//$pdf->SetX(40);
		$pdf->Cell(10,0,'',0,0,C); 
		//$pdf->Write(20, '上海鞍钢国际贸易有限公司('.$ck.')钢材出库单');
		$pdf->Ln();

        $pdf->SetFont('simhei','B',18);
		$pdf->SetX(130);
		$pdf->SetAligns("C");
		$pdf->Write(20, '交易费用账单');
		$pdf->Ln();

        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->SetX(20);
		$pdf->Write(10, '生成日期：');
        $pdf->Write(10, date("Y年m月d日",time()));
		$pdf->Ln();

		
		$pdf->SetX(18);
        $pdf->SetWidths(array(30,40,40,22,20,20,13,15,40,20)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("12");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetvAligns("C"); $pdf->SetAligns("C");
        $pdf->Row(array('合同号','卖方','买方','交易总金额','费用','手续费类型','价格','支付状态','手续费支付方','生成日期')); 
		
		

        foreach($list as $v)
        { 		
		
			if($v['sxfzf']==1 ||$v['sxfzf']==2  ){
				$info=$this->_dao->getRow("select ComName from sys_company  where ID='".$v['PayMid']."'"  );
				$v['ComName']=$info['ComName'];
			 }elseif($v['sxfzf']==3){
				$v['ComName']="双方共同支付";
			 }
			 if($v['sxflx']=="3"){
				$v['persxf']=$v['persxf'] * 100;
			 }

			  if($v['sxflx']=="1"){	
					$v['jg']=$v['Cost']."元/笔\t";
			  }elseif($v['sxflx']=="2" ){
					$v['jg']=$v['persxf']."元/吨\t";
			  }elseif($v['sxflx']=="3" ){
					$v['jg']=$v['persxf']."%/笔\t";
			  }else{
					$v['jg']=""."\t";
			  }	
			

			$comname1=$this->_dao->getOne("select ComName from  sys_company  where ID='".$v['Mid_Shipper']."' ");
			$comname2=$this->_dao->getOne("select ComName from  sys_company  where ID='".$v['Mid_Consignee']."' ");

			$pdf->SetX(18);
			$pdf->SetLeftMargin(18);
			$pdf->SetWidths(array(30,40,40,22,20,20,13,15,40,20));
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 8); 
            $pdf->SetvAligns("C"); $pdf->SetAligns("C");
			
            $pdf->Row(array($v['OrderNO'],$comname1,$comname2,$v['TotalMoney'],$v['Cost'],$SXFLX[$v['sxflx']],$v['jg'],$paystatus[$v['PayStatus']], $v['ComName'],$v['JyDate'])); 

          //  $pdf->SetXY(18);
        } 
		$h = $pdf->GetY();
		//$h = $pdf->GetY();
		
		$pdf->SetX(18);
		$pdf->SetWidths(array(30,40,40,22,20,20,13,15,40,20)); // 设置每列的宽度15,26,27,20,26,16,11,22,18,17
        $pdf->SetZeilenhoehe("7");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计','','',$money['total'],$money['cost'],'','','','','')); 

		$pdf->Output('费用账单.pdf', 'I'); //D or I
	}



    public function zthd($params)
    {
        $hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");
	//	include("code128.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail where TID = '" . $hth . "'");

		foreach($zydetail as &$tmp){
			$tmp['money'] = $tmp['BuyQuantity'] * $tmp['SalesPrice'];
		}
	file_get_contents("/barcode/code.php?codebar=BCGcode128&text=".$contact['ContractNo']);
	//	header("location:barcode/code.php?codebar=BCGcode128&text=".$contact['ContractNo']);continue;

        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
       
 
       //$new_code = new BarCode128($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
//$barcode = new BarCode128('',"DZWZ-YX-********-********","B");
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

		$totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail  WHERE TID = '" . $hth . "'");

		//总数量
		$totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail WHERE TID = '" . $hth . "'");


        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true);
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 14); //设置字体样式
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif",10,8);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 10, 10,70,10);
        // update by libing end for barcode in 2015/08/07
		//$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->SetX(120);
		$pdf->SetFont('simsun', '', 11); 
        $pdf->Write(10, '中铁物资集团华东有限公司');
		$pdf->Ln();

        $pdf->SetX(95);
		$pdf->SetFont('simsun', '', 8); 
        $pdf->Write(3, 'CHINA RAILWAY MATERIAL GROUP(EAST CHINA)CO.,LTD.');
		$pdf->Ln();

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
     //   $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);

        $pdf->Line(10, 25, 200, 25);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(60);
		$pdf->SetFont('simsun', 'B', 16);
        $pdf->Write(14, '中铁物资集团华东有限公司'.$GLOBALS['ZYTYPE'][$contact['SalesType']].'合同');
		 $pdf->Write(14, $aa);
        $pdf->Ln();

        $pdf->SetX(110);
		$pdf->SetFont('simsun', '', 12); 
		$pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);
		$pdf->Ln();


        function addline($txt1, $txt2, $txt3, $pdf)
        {
            $pdf->SetFont('simsun', '', 12);
            $pdf->Write(10, $txt1);
            $pdf->SetFont('simsun', 'U', 12); //设置字体样式
            $pdf->Write(10, $txt2);
            $pdf->SetFont('simsun', '', 12); //设置字体样式
            $pdf->Write(10, $txt3);
        } 


        //addline('（二）货物质量标准按', $contact['zlbz'], '标准执行。', $pdf);

        $pdf->SetX(110);
		$pdf->Write(10, '签订时间地点：');
		addline('',gmdate('Y',strtotime($contact['qdtime'])),'年', $pdf);
		addline('',gmdate('m',strtotime($contact['qdtime'])),'月', $pdf);
		addline('',gmdate('d',strtotime($contact['qdtime'])),'日', $pdf);

        $pdf->Write(10, $contact['qdaddress']);
		$pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '甲方(供方)：');
        $pdf->Write(10, $salecomp['ComName']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '乙方(需方)：');
        $pdf->Write(10, $buycomp['ComName']);

        $pdf->Ln();
        $pdf->SetX(16);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '甲乙双方就'.$GLOBALS['ZYTYPE'][$contact['SalesType']].'事宜，经平等、自愿协商，达成如下一致意见：');



        $pdf->Ln();


        $pdf->SetX(16);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '一、产品名称、材质、规格、产地、数量、单价、金额、交货期');
    $pdf->Ln();

		$pdf->Cell(25,12,'产品名称',1,0,'C');
		$pdf->Cell(20,12,'材质',1,0,'C');   
		$pdf->Cell(30,12,'规格（mm）',1,0,'C');
		$pdf->Cell(20,12,'产地',1,0,'C');
		$w = $pdf->GetX();
		$h = $pdf->GetY();
		$pdf->Cell(17,6,'数量',1,0,'C');
		
		$pdf->Ln();
		$pdf->SetX($w);
		$pdf->Cell(17,6,'（吨）',1,0,'C');
		
		$pdf->SetXY($w+17,$h);
		$pdf->Cell(25,6,'含税单价',1,0,'C');
		$pdf->Ln();
		$pdf->SetX($w+17);
		$pdf->Cell(25,6,'（元）',1,0,'C');
		$pdf->SetXY($w+42,$h);

		$pdf->Cell(30,6,'金额',1,0,'C');
		$pdf->Ln();
		$pdf->SetX($w+42);
		$pdf->Cell(30,6,'（元）',1,0,'C');
		$pdf->SetXY($w+72,$h);

		$pdf->Cell(22,12,'交货期',1,0,'C');
		$pdf->SetXY($w+120,$h);
		$pdf->Ln();

		//$pdf->SetX(18);
        $pdf->SetWidths(array(25,20,30,20,17,25,30)); // 设置每列的宽度
$num=1;
        foreach($zydetail as $v)
        { 
				
	
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 9); 
            $pdf->SetAligns("C");
			$A=$pdf->GetX();
            $pdf->Row(array($v['VarietyName'],$v['MaterialCode'],$v['SpecCode'],$v['OriginCode'],$v['BuyQuantity'],$v['SalesPrice'],$v['money'],$v['totaljg'])); 
			$num++;
            
        } 
$num=$num * 5;
		$h = $pdf->GetY();

		$pdf->SetWidths(array(95,17,25,30)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("5");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计',$totalweight,'',$totalmoney)); 


		$pdf->SetWidths(array(95,94)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 11); 
        $pdf->SetAligns("C");		
        // updated by libing for num2cny start 2016/01/06
        //$pdf->Row(array('人民币金额（大写）',$this->toCNcap($totalmoney))); 
        $pdf->Row(array('人民币金额（大写）',$this->cny($totalmoney))); 
        // updated by libing for num2cny end 2016/01/06
      //  $pdf->Ln();
$pdf->SetXY($w+72,$h-$num +5);
$pdf->SetWidths(array(22));
$pdf->SetZeilenhoehe("$num");
$pdf->Row(array($contact['PickUpDate'])); 
$pdf->Ln();	
		
	//	$pdf->Ln();

        $pdf->SetXY($w-89,$h+10);
        $pdf->SetFont('', '', 12); //设置字体样式
        $pdf->Write(10, '注：具体交易数量以实际交付为准，以含税单价乘以实际交付数量为交易金额。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '二、交（提）货地点和运输方式：');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '交货地点：'.$contact['PickUpAddress']);
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '运输方式： 汽运，甲方包到工地。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '三、相关费用承担：出库费、运费均由'.$GLOBALS['HTSF_2'][$contact['ysfy']].'承担。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '四、验收质量标准：执行生产厂家出厂标准。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '五、验收数量标准：执行生产厂家出厂标准。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '六、异议提出期限：乙方对货物享有终身提出异议的权力。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '七、结算方式及期限：');
		$pdf->Ln();
		$pdf->SetX(24);
		addline('乙方在合同签订后以', $GLOBALS['ZFKXS'][$contact['fkxs']] ,'方式预付甲方总货款的20%定金，剩余货款在货到验收合格后以现款方式一次性付清。甲方在乙方提完货后', $pdf);
		
		$pdf->Write(10, $contact['kjfp'].'日内开具增值税专用发票给乙方结算。');

		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '八、违约责任：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '1、合同签订当日办理发货事宜。');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '2、甲方收到合同订单后办理发货事宜，甲方应按时履行发货义务，如因甲方原因迟延发货，甲方按照定金的两倍向乙方支付违约金。并应承担因此而给乙方造成的其他损失。');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '3、其他均按照《中华人民共和国合同法》的相关规定执行。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '九、合同变更和解除：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '合同有效期内，双方应当如约履行合同。合同如有未尽事宜，须经双方共同协商，做出书面补充约定，补充约定与本合同具有同等效力。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '十、合同争议的解决方式：');
		$pdf->Ln();
		$pdf->SetX(24);
	//Update by xiakang for pdf started 2015/08/31
		//$pdf->Write(10, '本合同项下发生的争议，由双方当事人协商解决，协商不成的，可依法向合同签订地人民法院起诉。');
		if($contact['DisputeSettlement']=="1"){
		addline('本合同项下发生的争议，由双方当事人协商解决，协商不成的，提交'," ".$contact['DisputeSettlement_city']." ", '仲裁委员会仲裁。',$pdf);
		}
		if($contact['DisputeSettlement']=="2"){
		$pdf->Write(10, '本合同项下发生的争议，由双方当事人协商解决，协商不成的，向合同签订地人民法院起诉。');
		}
	//Update by xiakang for pdf ended 2015/08/31
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '十一、其它约定事项：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '任何一方由于不可抗力的原因不能履行合同时，应当及时向对方通报，在取得相关证明以后，可部分或者全部免予承担违约责任。本合同所称不可抗力是指双方在订立合同时不能预见并且不能避免、不能克服的客观情况，包括重大自然灾害、战争等。');
		$pdf->Ln();
		$pdf->SetX(16);
	//	$pdf->Write(10, '十二、本合同壹式肆份，双方各执贰份，双方签字盖章后生效。合同传真件具有同等法律效力。');
		addline('十二、本合同壹式肆份，双方各执贰份，双方签字盖章后生效。',合同传真件具有同等法律效力。,'', $pdf);



     
        $pdf->Ln();
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '甲方：');
		$pdf->Write(10, $this->c_substr($salecomp['ComName'],0,18));
        $pdf->SetX(115);
        $pdf->Write(10, '乙方：');
		$pdf->Write(10, $this->c_substr($buycomp['ComName'],0,16));
        $pdf->Ln();

	if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
		$pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
        $pdf->Ln();
	}
        $pdf->SetX(10);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $salecomp['Address']);

        $pdf->SetX(115);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $buycomp['Address']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $salecomp['ContactTel']);

        $pdf->SetX(115);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $salecomp['ContactFax']);

        $pdf->SetX(115);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10, $salecomp['BankType']);

        $pdf->SetX(115);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10, $buycomp['BankType']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $salebank['MBR_SPE_ACCT_NO']);

        $pdf->SetX(115);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $buybank['MBR_SPE_ACCT_NO']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $salecomp['TaxNo']);

        $pdf->SetX(115);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $buycomp['TaxNo']);
        $pdf->Ln();


        // $pdf->Uni_putpages();
        // 二维码
        $b = str_replace(' ', '', $contact['TwoDimCode']); 
        // $pdf->Write(10,$c);
        $c = WEB_SITE."/member.php?view=yzpdf&hid=" . $hth . "&dimcode=" . $b."&dtid=".$contact['BID'];

        QRcode::png($c, "images/ewm/" . $contact['ContractNo'] . ".png");

        $sc = urlencode($c);

        $xer = $pdf->GetX();
        $yer = $pdf->GetY();

        $pdf->Image("images/ewm/" . $contact['ContractNo'] . ".png", $xer, $yer);
		
        $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        
    } 

    public function ztgzj($params)
    {
        $hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");
		//include("code.class.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail where TID = '" . $hth . "'");
		foreach($zydetail as &$tmp){
			$tmp['money'] = $tmp['BuyQuantity'] * $tmp['SalesPrice'];
		}
		file_get_contents("/barcode/code.php?codebar=BCGcode128&text=".$contact['ContractNo']);

	//	$aa=$this->sese($contact['ContractNo']);
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 


//$barcode = new BarCode128($contact['ContractNo'] ,$contact['ContractNo'] );
//$barcode->createBarCode();

        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

		$totalmoney = $this->_dao->getOne("select SUM(BuyQuantity * SalesPrice) from sm_contract_transaction_detail  WHERE TID = '" . $hth . "'");

		//总数量
		$totalweight = $this->_dao->getOne("select SUM(BuyQuantity) from sm_contract_transaction_detail WHERE TID = '" . $hth . "'");


        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->Open();
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true);
        // $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 14); //设置字体样式
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif",10,8);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 10, 10,70,13);
        // update by libing end for barcode in 2015/08/07
        $pdf->SetX(10);
		$pdf->SetFont('simsun', '', 11); 
        $pdf->Write(10, '   ');
		$pdf->Ln();

        $pdf->SetX(95);
		$pdf->SetFont('simsun', '', 8); 
        $pdf->Write(3, '   ');
		$pdf->Ln();

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
     //   $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);

        $pdf->Line(10, 25, 200, 25);
        $pdf->Ln();
        $pdf->SetX(38);
		$pdf->SetFont('simsun', 'B', 16);
        $pdf->Write(14, '中铁物资集团钢之家电子商务有限公司'.$GLOBALS['ZYTYPE'][$contact['SalesType']].'合同');
        $pdf->Ln();

        $pdf->SetX(110);
		$pdf->SetFont('simsun', '', 12); 
		$pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);
		$pdf->Ln();

        function addline($txt1, $txt2, $txt3, $pdf)
        {
            $pdf->SetFont('simsun', '', 12);
            $pdf->Write(10, $txt1);
            $pdf->SetFont('simsun', 'U', 12); //设置字体样式
            $pdf->Write(10, $txt2);
            $pdf->SetFont('simsun', '', 12); //设置字体样式
            $pdf->Write(10, $txt3);
        } 

        $pdf->SetX(110);
		$pdf->Write(10, '签订时间地点：');
		addline('',gmdate('Y',strtotime($contact['qdtime'])),'年', $pdf);
		addline('',gmdate('m',strtotime($contact['qdtime'])),'月', $pdf);
		addline('',gmdate('d',strtotime($contact['qdtime'])),'日', $pdf);

        $pdf->Write(10, $contact['qdaddress']);
		$pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '甲方(供方)：');
        $pdf->Write(10, $salecomp['ComName']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '乙方(需方)：');
        $pdf->Write(10, $buycomp['ComName']);

        $pdf->Ln();
        $pdf->SetX(16);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '甲乙双方就'.$GLOBALS['ZYTYPE'][$contact['SalesType']].'事宜，经平等、自愿协商，达成如下一致意见：');



        $pdf->Ln();




        $pdf->SetX(16);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '一、产品名称、材质、规格、产地、数量、单价、金额、交货期');
    $pdf->Ln();

		$pdf->Cell(25,12,'产品名称',1,0,'C');
		$pdf->Cell(20,12,'材质',1,0,'C');   
		$pdf->Cell(30,12,'规格（mm）',1,0,'C');
		$pdf->Cell(20,12,'产地',1,0,'C');
		$w = $pdf->GetX();
		$h = $pdf->GetY();
		$pdf->Cell(17,6,'数量',1,0,'C');
		
		$pdf->Ln();
		$pdf->SetX($w);
		$pdf->Cell(17,6,'（吨）',1,0,'C');
		
		$pdf->SetXY($w+17,$h);
		$pdf->Cell(25,6,'含税单价',1,0,'C');
		$pdf->Ln();
		$pdf->SetX($w+17);
		$pdf->Cell(25,6,'（元）',1,0,'C');
		$pdf->SetXY($w+42,$h);

		$pdf->Cell(30,6,'金额',1,0,'C');
		$pdf->Ln();
		$pdf->SetX($w+42);
		$pdf->Cell(30,6,'（元）',1,0,'C');
		$pdf->SetXY($w+72,$h);

		$pdf->Cell(22,12,'交货期',1,0,'C');
		$pdf->SetXY($w+120,$h);
		$pdf->Ln();

		//$pdf->SetX(18);
        $pdf->SetWidths(array(25,20,30,20,17,25,30)); // 设置每列的宽度
$num=1;
        foreach($zydetail as $v)
        { 
				
	
            $pdf->SetZeilenhoehe("5");
            $pdf->SetFont('simsun', '', 9); 
            $pdf->SetAligns("C");
			$A=$pdf->GetX();
            $pdf->Row(array($v['VarietyName'],$v['MaterialCode'],$v['SpecCode'],$v['OriginCode'],$v['BuyQuantity'],$v['SalesPrice'],$v['money'],$v['totaljg'])); 
			$num++;
            
        } 
$num=$num * 5;
		$h = $pdf->GetY();

		$pdf->SetWidths(array(95,17,25,30)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("5");
        $pdf->SetFont('simsun', 'B', 9); 
        $pdf->SetAligns("C");		
        $pdf->Row(array('合计',$totalweight,'',$totalmoney)); 


		$pdf->SetWidths(array(95,94)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 11); 
        $pdf->SetAligns("C");		
        // updated by libing for num2cny start 2016/01/06
        //$pdf->Row(array('人民币金额（大写）',$this->toCNcap($totalmoney))); 
        $pdf->Row(array('人民币金额（大写）',$this->cny($totalmoney))); 
        // updated by libing for num2cny end 2016/01/06
      //  $pdf->Ln();
$pdf->SetXY($w+72,$h-$num +5);
$pdf->SetWidths(array(22));
$pdf->SetZeilenhoehe("$num");
$pdf->Row(array($contact['PickUpDate'])); 
$pdf->Ln();
        $pdf->SetXY($w-89,$h+10);
        $pdf->SetFont('', '', 12); //设置字体样式
        $pdf->Write(10, '注：具体交易数量以实际交付为准，以含税单价乘以实际交付数量为交易金额。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '二、交（提）货地点和运输方式：');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '交货地点：'.$contact['PickUpAddress']);
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '运输方式： 汽运，甲方包到工地。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '三、相关费用承担：出库费、运费均由'.$GLOBALS['HTSF_2'][$contact['ysfy']].'承担。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '四、验收质量标准：执行生产厂家出厂标准。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '五、验收数量标准：执行生产厂家出厂标准。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '六、异议提出期限：乙方对货物享有终身提出异议的权力。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '七、结算方式及期限：');
		$pdf->Ln();
		$pdf->SetX(24);
		addline('乙方在合同签订后以', $GLOBALS['ZFKXS'][$contact['fkxs']] ,'方式预付甲方总货款的20%定金，剩余货款在货到验收合格后以现款方式一次性付清。甲方在乙方提完货后', $pdf);
		
		$pdf->Write(10, $contact['kjfp'].'日内开具增值税专用发票给乙方结算。');

		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '八、违约责任：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '1、合同签订当日办理发货事宜。');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '2、甲方收到合同订单后办理发货事宜，甲方应按时履行发货义务，如因甲方原因迟延发货，甲方按照定金的两倍向乙方支付违约金。并应承担因此而给乙方造成的其他损失。');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '3、其他均按照《中华人民共和国合同法》的相关规定执行。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '九、合同变更和解除：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '合同有效期内，双方应当如约履行合同。合同如有未尽事宜，须经双方共同协商，做出书面补充约定，补充约定与本合同具有同等效力。');
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '十、合同争议的解决方式：');
		$pdf->Ln();
		$pdf->SetX(24);
		//Update by xiakang for pdf started 2015/08/31
		//$pdf->Write(10, '本合同项下发生的争议，由双方当事人协商解决，协商不成的，可依法向合同签订地人民法院起诉。');
		if($contact['DisputeSettlement']=="1"){
		addline('本合同项下发生的争议，由双方当事人协商解决，协商不成的，提交'," ".$contact['DisputeSettlement_city']." ", '仲裁委员会仲裁。',$pdf);
		}
		if($contact['DisputeSettlement']=="2"){
		$pdf->Write(10, '本合同项下发生的争议，由双方当事人协商解决，协商不成的，向合同签订地人民法院起诉。');
		}
		//Update by xiakang for pdf started 2015/08/31
		$pdf->Ln();
		$pdf->SetX(16);
		$pdf->Write(10, '十一、其它约定事项：');
		$pdf->Ln();
		$pdf->SetX(24);
		$pdf->Write(10, '任何一方由于不可抗力的原因不能履行合同时，应当及时向对方通报，在取得相关证明以后，可部分或者全部免予承担违约责任。本合同所称不可抗力是指双方在订立合同时不能预见并且不能避免、不能克服的客观情况，包括重大自然灾害、战争等。');
		$pdf->Ln();
		$pdf->SetX(16);
	//	$pdf->Write(10, '十二、本合同壹式肆份，双方各执贰份，双方签字盖章后生效。合同传真件具有同等法律效力。');
		addline('十二、本合同壹式肆份，双方各执贰份，双方签字盖章后生效。',合同传真件具有同等法律效力。,'', $pdf);



     
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->Write(10, '甲方：');
		$pdf->Write(10, $this->c_substr($salecomp['ComName'],0,18));
        $pdf->SetX(115);
        $pdf->Write(10, '乙方：');
		$pdf->Write(10, $this->c_substr($buycomp['ComName'],0,16));
        $pdf->Ln();

	if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
		$pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
        $pdf->Ln();
	}

        $pdf->SetX(10);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '授权代表签字：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '盖章：');
        $pdf->Write(10, '');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $salecomp['Address']);

        $pdf->SetX(115);
        $pdf->Write(10, '地址：');
        $pdf->Write(10, $buycomp['Address']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $salecomp['ContactTel']);

        $pdf->SetX(115);
        $pdf->Write(10, '电话：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $salecomp['ContactFax']);

        $pdf->SetX(115);
        $pdf->Write(10, '传真：');
        $pdf->Write(10, $buycomp['ContactTel']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10, $salecomp['BankType']);

        $pdf->SetX(115);
        $pdf->Write(10, '开户银行：');
        $pdf->Write(10, $buycomp['BankType']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $salebank['MBR_SPE_ACCT_NO']);

        $pdf->SetX(115);
        $pdf->Write(10, '银行账号：');
        $pdf->Write(10, $buybank['MBR_SPE_ACCT_NO']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $salecomp['TaxNo']);

        $pdf->SetX(115);
        $pdf->Write(10, '税号：');
        $pdf->Write(10, $buycomp['TaxNo']);
        $pdf->Ln();


        // $pdf->Uni_putpages();
        // 二维码
        $b = str_replace(' ', '', $contact['TwoDimCode']); 
        // $pdf->Write(10,$c);
        $c = WEB_SITE."/member.php?view=yzpdf&hid=" . $hth . "&dimcode=" . $b."&dtid=".$contact['BID'];

        QRcode::png($c, "images/ewm/" . $contact['ContractNo'] . ".png");

        $sc = urlencode($c);

        $xer = $pdf->GetX();
        $yer = $pdf->GetY();

        $pdf->Image("images/ewm/" . $contact['ContractNo'] . ".png", $xer, $yer);
	
        $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        
    } 


public function toCNcap($data) {  
   $capnum=array("零","壹","贰","叁","肆","伍","陆","柒","捌","玖");
   $capdigit=array("","拾","佰","仟");
   $subdata=explode(".",$data);
   $yuan=$subdata[0];
   $j=0; $nonzero=0;
   for($i=0;$i<strlen($subdata[0]);$i++) {  
      if(0==$i) { //确定个位  
         if(isset($subdata[1])) {  
            $cncap=(substr($subdata[0],-1,1)!=0)?"元":"元零";
         }else{  
            $cncap="元";
         }  
      }    
      if(4==$i) { $j=0; $nonzero=0; $cncap="万".$cncap; } //确定万位  
      if(8==$i) { $j=0; $nonzero=0; $cncap="亿".$cncap; } //确定亿位  
      $numb=substr($yuan,-1,1); //截取尾数  
      $cncap=($numb)?$capnum[$numb].$capdigit[$j].$cncap:(($nonzero)?"零".$cncap:$cncap);
      $nonzero=($numb)?1:$nonzero;
      $yuan=substr($yuan,0,strlen($yuan)-1); //截去尾数    
      $j++;
   }  
  $chiao = $cent = "";
  $zhen = "整";
   if(isset($subdata[1])) {  
     $chiao=(substr($subdata[1],0,1))?$capnum[substr($subdata[1],0,1)]."角":"零";
     $cent=(substr($subdata[1],1,1))?$capnum[substr($subdata[1],1,1)]."分":"零分";
     $zhen="";
   }  
 
   $cncap .= $chiao.$cent.$zhen;
   $cncap=preg_replace("/(零)+/","\\1",$cncap); //合并连续“零”  
   return $cncap;
}   




function cd_barras($value,$files,$into=1) {
require_once('barcode/class/BCGFontFile.php');
require_once('barcode/class/BCGColor.php');
require_once('barcode/class/BCGDrawing.php');


$codebar = $_REQUEST['codebar']; //条形码将要数据的内容

// Including the barcode technology
require_once('barcode/class/'.$codebar.'.barcode.php');

// Including the barcode technology
//require_once('class/BCGcode128.barcode.php');

// Loading Font
$font = new BCGFontFile('./barcode/font/Arial.ttf', 18);

// Don't forget to sanitize user inputs
//$text = isset($_GET['text']) ? $_GET['text'] : '';

// The arguments are R, G, B for color.
$color_black = new BCGColor(0, 0, 0);
$color_white = new BCGColor(255, 255, 255);

$drawException = null;
try {
	$code = new $codebar();
	$code->setScale(2); // Resolution
	$code->setThickness(30); // Thickness
	$code->setForegroundColor($color_black); // Color of bars
	$code->setBackgroundColor($color_white); // Color of spaces
	$code->setFont($font); // Font (or 0)
	$code->parse($value); // Text
} catch(Exception $exception) {
	$drawException = $exception;
}

/* Here is the list of the arguments
1 - Filename (empty : display on screen)
2 - Background color */
$drawing = new BCGDrawing('', $color_white);
if($drawException) {
	$drawing->drawException($drawException);
} else {
	$drawing->setBarcode($code);
	$drawing->draw();
}

// Header that says it is an image (remove it if you save the barcode to a file)
header('Content-Type: image/png');
header('Content-Disposition: inline; filename="barcode.png"');
$this->put_img($img,$files);
// Draw (or save) the image into PNG format.
$drawing->finish(BCGDrawing::IMG_FORMAT_PNG);

 }	

//Added by quanjw for meijiao start 2015/7/17
//将资源分类 
function groupResourse($zydetail){
    //表格区分 煤炭 焦炭 水泥 金属 钢材 铁矿石
    $dz_or_ygw = DZ_OR_YGW;
    foreach ($zydetail as $key => $value) {
        //Added by hezp for 17307 started 2015/10/15
        if( $dz_or_ygw =='1' || $dz_or_ygw =='2' ){

            switch($value["Vid"]){
                case $GLOBALS["MEITAN_VID"]:
                    $zyl_meitan[]=$value;
                    break;
                case $GLOBALS["JIAOTAN_VID"];
                    $zyl_jiaotan[]=$value;
                    break;
                case $GLOBALS["SHUINI_VID"];
                    $zyl_shuini[]=$value;
                    break;
                case $GLOBALS["JINSHU_VID"];
                    $zyl_jinshu[]=$value;
                    break;
                case $GLOBALS["TKS_VID"];
                    $zyl_tks[]=$value;
                    break;
                case $GLOBALS["GTYL_VID"];
                    $zyl_gtyl[]=$value;
                    break;
                case $GLOBALS["THJ_VID"];
                    $zyl_thj[]=$value;
                    break;
                case $GLOBALS["YS_VID"];
                    $zyl_ys[]=$value;
                    break;
                case $GLOBALS["HG_VID"];
                    $zyl_hg[]=$value;
                    break;
                default :
                    $zyl_zy[]=$value;
            }
        }else{
            switch($value["Vid"]){
                case $GLOBALS["MEITAN_VID"]:
                    $zyl_meitan[]=$value;
                    break;
                case $GLOBALS["JIAOTAN_VID"];
                    $zyl_jiaotan[]=$value;
                    break;
                case $GLOBALS["SHUINI_VID"];
                    $zyl_shuini[]=$value;
                    break;
                case $GLOBALS["JINSHU_VID"];
                    $zyl_jinshu[]=$value;
                    break;
                default :
                    $zyl_zy[]=$value;
            }
        }
        //Added by hezp for 17307 started 2015/10/15
    }
    //非空
    if(!empty($zyl_meitan)){
        $zyll[$GLOBALS["MEITAN_VID"]]=$zyl_meitan;
    }
    if(!empty($zyl_jiaotan)){
        $zyll[$GLOBALS["JIAOTAN_VID"]]=$zyl_jiaotan;
    }
    if(!empty($zyl_shuini)){
        $zyll[$GLOBALS["SHUINI_VID"]]=$zyl_shuini;
    }
    if(!empty($zyl_jinshu)){
        $zyll[$GLOBALS["JINSHU_VID"]]=$zyl_jinshu;
    }
    if(!empty($zyl_tks)){
        $zyll[$GLOBALS["TKS_VID"]]=$zyl_tks;
    }
    //Added by hezp for 17307 started 2015/10/15
    if( DZ_OR_YGW =='1' || DZ_OR_YGW =='2' ){
        if(!empty($zyl_ks)){
            $zyll[$GLOBALS["TKS_VID"]]=$zyl_tks;
        }
        if(!empty($zyl_gtyl)){
            $zyll[$GLOBALS["GTYL_VID"]]=$zyl_gtyl;
        }
        if(!empty($zyl_thj)){
            $zyll[$GLOBALS["THJ_VID"]]=$zyl_thj;
        }
        if(!empty($zyl_ys)){
            $zyll[$GLOBALS["YS_VID"]]=$zyl_ys;
        }
        if(!empty($zyl_hg)){
            $zyll[$GLOBALS["HG_VID"]]=$zyl_hg;
        }
    }
    //Added by hezp for 17307 ended 2015/10/15
    if(!empty($zyl_zy)){
        $zyll[$GLOBALS["ZY_VID"]]=$zyl_zy;
    }
    return $zyll;
} 
//Added by quanjw for meijiao end 2015/7/17


	
	//鹤山恒基 发货单 现 所有专区发货单
	public function hshj_send($params){
		$DDId = $params['DDId'];
		$zone =  $params['zone'];
		
		$ht = $this->_dao->getRow("select * from sm_contract_transaction  where BID = '" . $DDId . "'");
		$htid = $ht['ID'];
		
		$this->assign( "Delivery", $ht['Delivery'] );
		
		$OrderNo = $this->_dao->getOne("select OrderNo from sm_exc_dd_tag where ID = '". $DDId."'");
		$this->assign( "OrderNo", $OrderNo );
		
		$td  = $this->_dao->getRow("select *  from sm_contract_transaction_td where Tid = '". $htid."'");
        $this->assign( "Td", $td );
		
		// 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $ht['Mid_Consignee'] . "' "); 
		$this->assign( "buycomp", $buycomp );
	   // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $ht['Mid_Shipper'] . "' ");
		$this->assign( "salecomp", $salecomp );
		
		$billList = $this->_dao->query("select sm_exc_dd.*,sm_exc_order_detail.*,sm_exc_sales_details.xincengWeight,sm_exc_sales_details.tks_fe,sm_exc_sales_details.tks_si,sm_exc_sales_details.tks_al,sm_exc_sales_details.strength,sm_exc_sales_details.Vid,sm_exc_sales_details.cd from sm_exc_order_detail,sm_exc_dd ,sm_exc_sales_details where sm_exc_dd.ID=sm_exc_order_detail.Ddid and sm_exc_dd.sdid = sm_exc_sales_details.id  and  sm_exc_order_detail.Status=1 and sm_exc_dd.Dtid = ".$DDId." order by sm_exc_order_detail.ID ASC",0);
		if( $zone != 0){
			foreach($billList as &$bill){
				/* 煤炭：品名 材质（挥发分/灰分/全硫分)
				焦炭：品名 材质（灰分/硫分/CSR)
				水泥: 品名 规格
				金属制品：品名 材质（强度/锌层重量) 规格 */
				$Vid = $bill['Vid'];
				if($Vid == $GLOBALS["MEITAN_VID"]){
					$bill['caizhi'] = $bill['caizhi'].'|'.$bill['guige'].'|'.$bill['cd'];
					unset($bill['guige']);
					unset($bill['cd']);
				}else if($Vid == $GLOBALS["JIAOTAN_VID"]){
					$bill['caizhi'] = $bill['guige'].'|'.$bill['caizhi'].'|'.$bill['cd'];
					unset($bill['guige']);
					unset($bill['cd']);
				}else if($Vid == $GLOBALS["SHUINI_VID"]){
					unset($bill['caizhi']);
				}else if($Vid == $GLOBALS["TKS_VID"]){
					$bill['caizhi'] ="Fe:".$bill['tks_fe']."%";
				}else if($Vid == $GLOBALS["JINSHU_VID"]){
					$bill['caizhi'] = $bill['strength'].'|'.$bill['xincengWeight'];
				}
			}
		}
		$this->assign( "billList", $billList );
		
		$this->assign( "zone", $zone );
		if($zone == '1'){//钢城钢之家 需要联系人信息
			//联系人信息
			$this->assign( "ht", $ht );
			//车牌号
			//$Carnum=$this->_dao->getOne("select Carnum from sm_exc_order  where   Did='".$DDId."'");
			$Carnum=$this->_dao->getRow("select FaHuoCheNo,TiHuoCheNo from zoneapp_contract_transcation_status  where   dzcontract_id='".$ht['ID']."'");
			$this->assign( "Carnum", $Carnum );
		}
	
	}
	//added by hezp started 2016/03/30
	//鹤山恒基合同
	public function hshj_contract($params){

        $hth = $params['hth'];

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
		//print_r($contact);
		$fkfs=$GLOBALS['ZFKXS'][$contact['fkxs']];
		$this->assign( "contact", $contact );
		$this->assign( "fkfs", $fkfs );
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $hth . "'");
		$this->assign( "zydetail", $zydetail );

		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
		$this->assign( "zy", $zy );
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
		$this->assign( "buycomp", $buycomp );
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' ");
		$this->assign( "salecomp", $salecomp );

	}
	//added by hezp ended 2016/03/30
	//added by hezp started for gcgzj 2016/05/25
	public function IsechomeGetHTPdf($params)
    {
        
		$DdId = $params['DdId'];
		

        include("/usr/local/www/www.zgdzwz.com/classfile.php");
        include("/usr/local/www/www.zgdzwz.com/phpqrcode.php");
        //include("/usr/local/www/www.zgdzwz.com/chinese.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where BID = '" . $DdId . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $contact['ID'] . "'");
		$zydetail = $this->zy_format($zydetail);
		$zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
		//print_r($zy);
		//updated by hzp ,竞价也当协议价20150806 started
		if($zy['bjfs']=="2" || $zy['bjfs'] == "3"){
			$zy['bjfs'] = "3";
		}
		//updated by hzp ,竞价也当协议价20150806 end
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' "); 
		//added by hzp for yunqian started 2015/08/05
		if($buycomp['signCompanyname'] !=""){
			$buycomp['ComName'] = $buycomp['signCompanyname'];
		}else{
			$buycomp['ComName'] = $buycomp['ComName'];
		}
		//added by hzp for yunqian ended 2015/08/05
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
		//added by hzp for yunqian started 2015/08/05
		if($salecomp['signCompanyname'] !=""){
			$salecomp['ComName'] = $salecomp['signCompanyname'];
		}else{
			$salecomp['ComName'] = $salecomp['ComName'];
		}
		//added by hzp for yunqian ended 2015/08/05
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
		$pdf->AddGBFont('kaiti', '华文楷体');

        $pdf->Open();
        $pdf->AddPage();
		
		
		// XK start deleted for login 2014/11/28
        //$pdf->SetAutoPageBreak(true);
		// XK end deleted for login 2014/11/28
        
		
		// $pdf->SetFont('simhei','',20);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        
        $pdf->SetX(10);
        $pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 130, 10,70,8);
        // update by libing end for barcode in 2015/08/07

        $pdf->Line(10, 20, 200, 20);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(80);
		$pdf->SetFont('simsun', 'B', 14);
        $pdf->Write(9, '工矿产品购销合同');
        $pdf->Ln();

        $pdf->SetX(10);
		$pdf->SetFont('simsun', 'B', 12);
        $pdf->Write(10, '甲方：');
        $pdf->Write(10, $salecomp['ComName']);
        
        $pdf->SetX(130);
		$pdf->SetFont('simsun', 'B', 12);
		$pdf->Write(10,'签订地点：');
		$pdf->Write(10, $contact['qdaddress']);
        $pdf->Ln();


        $pdf->SetX(10);
        $pdf->Write(10, '乙方：');
        $pdf->Write(10, $buycomp['ComName']);

        $pdf->SetX(130);
		$pdf->Write(10,'签订时间：');
		$pdf->Write(10, $contact['qdtime']);
        

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '一 货物名称、材质、规格、生产厂家、数量、含税单价、金额：');

        
		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', '', 10); //设置字体样式
        $pdf->SetAligns("center");
				
			
		$pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
		$pdf->Row(array('名称', '材质', '规格', '生产厂家', '数量（吨）', '单价（元/吨）', '金额（元）','备注')); // 设置列名    
			
		foreach($zydetail as $v){
				//输出资源详细信息
				$dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
				$pdf->SetZeilenhoehe("6");
				$pdf->SetFont('simsun', '', 11); //设置字体样式
				$pdf->SetAligns("center");
				
				$pdf->Row(array(html_entity_decode($v['VarietyName']), html_entity_decode($v['MaterialCode']), html_entity_decode($v['SpecCode']), html_entity_decode($v['OriginCode']), html_entity_decode($v['BuyQuantity']), html_entity_decode($v['SalesPrice']), $dtjgMoney,'')); // 设置列名    
				
		}
		$pdf->Ln();
		
		
		$pdf->SetWidths(array(189)); // 设置每列的宽度
		$pdf->Row(array('总计（人民币大写）：'. $this->cny($contact['TotalMoney']) ) ); // 设置列名
        
		$pdf->SetX(10);
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        $pdf->Write(9, '注：1、单价仅作为签订合同参考价格。');
		$pdf->Ln();
		$pdf->SetX(17);
		$pdf->Write(9, '2、具体交易数量以实际交付为准，以单价乘以实际交付数量为交易金额。');
		

        function addline($txt1, $txt2, $txt3, $pdf)
        {
            $pdf->SetFont('simsun','', 12);
            $pdf->Write(10, $txt1);
            //$pdf->SetFont('simsun', 'U', 12); //设置字体样式
			 $pdf->SetFont('kaiti', 'U', 12); //设置字体样式
            $pdf->Write(10, $txt2);
            $pdf->SetFont('simsun', '', 12); //设置字体样式
            $pdf->Write(10, $txt3);
        } 

        $pdf->Ln();
        $pdf->SetX(10);

        addline('二 产品质量要求：货物质量标准按 ',   "  国家标准  "  , ' 标准执行。甲方对质量负责的条件：见产品质量证明书。', $pdf);

       

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '三 交货日期、地点及收货单位');

        $pdf->Ln();
        addline('1、最迟交货日期：', "   ".$contact['PickUpDate']."   ", '。', $pdf);

        $pdf->Ln();
        addline('2、交货地点：', "   ".$contact['PickUpAddress']."   ", '。', $pdf);

        $pdf->Ln();
		addline('3、收（提）货单位：', "   ".$buycomp['ComName']."   ", '。', $pdf);
        
		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '四 运输方式、费用和交付条件');

		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
		$pdf->Write(10,'1、运输方式：');
		
		$pdf->SetFont('simsun', '', 12);
		$x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
	    if ($contact['Delivery'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '    自提   ');
		
		$x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
	    if ($contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x8, $y8);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x8, $y8);
        } 
        $pdf->Write(10, '    代办（');


		$pdf->SetFont('simsun', 'U', 12);
		$x5 = $pdf->GetX();
        $y5 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "1" && $contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x5, $y5);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x5, $y5);
        } 
        $pdf->Write(10, '     汽车运输  ');
		
		$x6 = $pdf->GetX();
        $y6 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "2"  && $contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x6, $y6);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x6, $y6);
        } 
        $pdf->Write(10, '     火车运输  ');
		
		$x7 = $pdf->GetX();
        $y7 = $pdf->GetY();
        if ($contact['ShipperYsfs'] == "3"  && $contact['Delivery'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x7, $y7);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x7, $y7);
        } 
        $pdf->Write(10, '     水运）。');

		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun','',12); //设置字体样式
		$pdf->Write(10, '运杂费用由乙方承担,一票结算。');
       


		
        
		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun','',12); //设置字体样式
        addline('2、乙方应将指定收货人的姓名、身份证号码，车船号等信息书面提前通知甲方。如果乙方在提货单记载的交货日期内提取货物，交付货物时甲方应提供磅码单、货物出厂质量证明书等货物随行相关证明一式',"   ".$contact['xgzs']."   ",'份。' , $pdf);

		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '3、货物交付给乙方之前，货物的损毁，灭失和失窃的风险由甲方承担。乙方指定的收/提货人在发/提货单上签字，即视为货权已经转移给乙方。');
   
		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '五、验收');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、数量确认：乙方提取货物时以（ ');

        $x3 = $pdf->GetX();
        $y3 = $pdf->GetY();
        if ($contact['hwsl'] == "1")
        {
            $pdf->Image("images/gou1.jpg", $x3, $y3);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x3, $y3);
        } 
        $pdf->Write(10, '     过磅  ');

        $x4 = $pdf->GetX();
        $y4 = $pdf->GetY();
        if ($contact['hwsl'] == "2")
        {
            $pdf->Image("images/gou1.jpg", $x4, $y4);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x4, $y4);
        } 
        $pdf->Write(10, '     理计）确认接收货物数量。由乙方或乙方指定的提货人或第一承运人在提货单中签字或盖章确认。交货数量按本合同约定数量的±5%的范围控制，超出此范围，双方协商处理。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2、质量检验：乙方按本合同约定的质量标准进行验收，在货物交付后15日内向甲方提出书面异议，由双方协商解决；逾期未提出书面异议，视为乙方已认可货物质量符合本合同约定。');

        $pdf->Ln();
		$pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'3、发票开具：甲方按实际结算金额开具等额有效发票，发票记载的货物品名、规格型号等信息必须与本合同约定一致，否则，乙方可要求甲方重开，因此产生的费用由甲方承担。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '六、结算方式及付款期限');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、结算方式： ');
		//addline('以'.$GLOBALS['PAYTYPE'][$contact['PayType']].'方式交易，乙方在本合同签订后的', "   ".$contact['fkqx']."   ", '个工作日内',$pdf);
		addline('以'.$GLOBALS['PAYTYPE'][$contact['PayType']].'方式交易，乙方在本合同签订后的1个工作日内支付', "   ".$contact['lybzj']."   ", '%货款到甲方帐户。',$pdf);
		addline('甲方在收到', "  ".$contact['lybzj']."  ", '%  货款后，通知乙方提货或按照要求将货物送至指定的交货地点。',$pdf);		
		
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2、付款方式： ');
        $x = $pdf->GetX();
        $y = $pdf->GetY();
        if ($contact['fkxs'] == "1" || $contact['fkxs'] == "2" || $contact['fkxs'] == "3")
        {
            $pdf->Image("images/gou1.jpg", $x, $y);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x, $y);
        } 
        $pdf->Write(10, '     银行汇款     ');

        $x2 = $pdf->GetX();
        $y2 = $pdf->GetY();

        if ($contact['fkxs'] == "4")
        {
            $pdf->Image("images/gou1.jpg", $x2, $y2);
        } 
        else
        {
            $pdf->Image("images/gou2.jpg", $x2, $y2);
        } 
		addline('     ', "  ".$contact['cdhp']."  ", '天银行承兑汇票，贴现等相关费用由 乙方 承担。',$pdf);

		
        //addline('贴现等相关费用由', "   ".$GLOBALS['HTSF_2'][$contact['txcd']]."   ", '承担。', $pdf);
                

		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '3、结算价格： ');
        $pdf->Write(10, '  以本合同签订的货物单价作为结算价格，不可更改。');       


        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '七、违约责任');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1、甲方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '1）甲方交付产品的材质、规格和质量等不符合本合同规定的，如果乙方同意使用，应当另行协商；如果乙方不能使用或不同意使用，甲方应无条件换货、退货并承担相应费用或赔偿乙方由此受到的经济损失。');
		
		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2）甲方无正当理由单方解除合同的，应立即退还乙方支付的货款并赔偿由此给乙方造成的经济损失。');

       

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '2、乙方违约责任：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
		$pdf->Write(10, '1）乙方无正当理由单方解除合同的，按合同金额的百分之二十赔偿给甲方。', $pdf);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        if($contact['DisputeSettlement']=="1"){
			$pdf->Write(10, '八、争议解决：若本合同在履行过程中发生争议，由双方协商解决或申请调解解决；协商或调解不成，提交'." ".$contact['DisputeSettlement_city']." ". '仲裁委员会仲裁。');
        }else if($contact['DisputeSettlement']=="2"){
			$pdf->Write(10, '八、争议解决：若本合同在履行过程中发生争议，由双方协商解决或申请调解解决；协商或调解不成，向合同签定所在地人民法院提出诉讼。');
        }


		$pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '九、本合同一式两份，双方各执一份，具有同等法律效力，传真复印件有效。');

        
		
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->Write(10, '供方：');
		$pdf->Write(10, $this->c_substr($salecomp['ComName'],0,18));
        $pdf->SetX(115);
        $pdf->Write(10, '需方：');
		$pdf->Write(10, $this->c_substr($buycomp['ComName'],0,16));
        $pdf->Ln();

	if(strlen($salecomp['ComName']) > 36 || strlen($buycomp['ComName']) > 32){
		$pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['ComName'],18,strlen($salecomp['ComName'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['ComName'],16,strlen($buycomp['ComName'])));
        $pdf->Ln();
	}
		$pdf->SetX(10);
        $pdf->Write(10, '单位地址：');
        $pdf->Write(10, $this->c_substr($salecomp['Address'],0,15));

        $pdf->SetX(115);
        $pdf->Write(10, '单位地址：');
        $pdf->Write(10, $this->c_substr($buycomp['Address'],0,13));
        $pdf->Ln();

	if(strlen($salecomp['Address']) > 30 || strlen($buycomp['Address']) > 26){
		$pdf->SetX(25);
        
        $pdf->Write(10, $this->c_substr($salecomp['Address'],15,strlen($salecomp['Address'])));

        $pdf->SetX(130);
        
        $pdf->Write(10, $this->c_substr($buycomp['Address'],13,strlen($buycomp['Address'])));
        $pdf->Ln();
	}

        $pdf->SetX(10);
        $pdf->Write(10, '法人代表：');
        $pdf->Write(10, $salecomp['Frdb']);

        $pdf->SetX(115);
        $pdf->Write(10, '法人代表：');
        $pdf->Write(10, $buycomp['Frdb']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '委托代理人（签字）：');
        $pdf->Write(10, '');

        $pdf->SetX(115);
        $pdf->Write(10, '委托代理人（签字）：');
        $pdf->Write(10, '');
        $pdf->Ln();
	

        $pdf->SetFont('simsun', '', 12);
        
        //added by weidianshang
        if($params['weidianshang']=='1'){
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I');
        }else{
		    echo BASE64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }
		 //$pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I

		//echo json_encode($arr);
        
    }
	//added by hezp ended 2016/05/25

	//add changhong 2018-08-17 马钢长材销售合同
	public function mg_hetong($params){
				$hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        // 资源信息
        $zydetail = $this->_dao->query("select * from sm_contract_transaction_detail,sm_contract_transaction where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and TID = '" . $hth . "'");
        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");
        
        $new_code = new cd_barra($contact['ContractNo'], "images/txm/" . $contact['ContractNo'] . ".gif", 1); 
        
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' ");
        
        if($buycomp['signCompanyname'] !=""){
            $buycomp['ComName'] = $buycomp['signCompanyname'];
        }else{
            $buycomp['ComName'] = $buycomp['ComName'];
        }
        
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 
        if($salecomp['signCompanyname'] !=""){
            $salecomp['ComName'] = $salecomp['signCompanyname'];
        }else{
            $salecomp['ComName'] = $salecomp['ComName'];
        }
        
        // 银行信息
        $buybank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Consignee'] . "' limit 1");
        $salebank = $this->_dao->getRow("select * from sm_user_ccb where MID='" . $contact['Mid_Shipper'] . "' limit 1");

        $pdf = new PDF_Chinese();
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->AddGBFont('kaiti', '华文楷体');

        $pdf->Open();
        $pdf->AddPage();
        
        function addline($txt1, $txt2, $txt3, $pdf)
        {
            $pdf->SetFont('simsun','', 12);
            $pdf->Write(10, $txt1);
            //$pdf->SetFont('simsun', 'U', 12); //设置字体样式
             $pdf->SetFont('kaiti', 'U', 12); //设置字体样式
            $pdf->Write(10, $txt2);
            $pdf->SetFont('simsun', '', 12); //设置字体样式
            $pdf->Write(10, $txt3);
        } 
        // XK start deleted for login 2014/11/28
        //$pdf->SetAutoPageBreak(true);
        // XK end deleted for login 2014/11/28
        
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        
        $pdf->SetX(10);
        $pdf->Write(10, '合同编号：');
        $pdf->Write(10, $contact['ContractNo']);

        $x8 = $pdf->GetX();
        $y8 = $pdf->GetY();
        // 条形码
        // update by libing start for barcode in 2015/08/07
        //$pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 160, 5);
        $pdf->Image("images/txm/" . $contact['ContractNo'] . ".gif", 130, 10,70,8);
        // update by libing end for barcode in 2015/08/07

        $pdf->Line(10, 20, 200, 20);
        $pdf->Ln();
        $pdf->Ln();
        $pdf->SetX(50);
        $pdf->SetFont('simsun', 'B', 14);
        $pdf->Write(9, $salecomp['ComName'].'钢材销售合同');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12);
        $pdf->SetFont('simhei', '', 12);
        $pdf->Write(10, '甲方（供方）'.$salecomp['ComName']);
        
        $pdf->SetX(115);
        $pdf->SetFont('simsun', '', 12);
        $pdf->Write(10,'合同编号：');
        $pdf->Write(10, $contact['ContractNo']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->Write(10, '   ');
        

        $pdf->SetX(115);
        $pdf->Write(10,'签订地点：');
        $pdf->Write(10, $contact['qdaddress']);
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->SetFont('simsun', '', 12);
        $pdf->Write(10, '乙方（需方）'.$buycomp['ComName']);

        $pdf->SetX(115);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'签订日期：');
        //$pdf->Write(10, $contact['qdtime']);
        addline('', date("Y",strtotime($contact['qdtime'])), '年', $pdf);
        addline('', date("m",strtotime($contact['qdtime'])), '月', $pdf);
        addline('', date("d",strtotime($contact['qdtime'])), '日', $pdf);
        $pdf->Ln();

        $pdf->SetX(20);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '双方经友好协商，依据《中华人民共和国合同法》及有关规定，现就本合同有关事项达成以下条款：');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '一、产品规格');

        $pdf->Ln();
        $pdf->SetX(10);
        //$pdf->SetWidths(array(24, 24, 24, 22, 22, 29, 22,22)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        //$pdf->SetAligns("Center");
        
        
        //资源分类
        $zyll = $this->groupResourse($zydetail);
        //echo "<pre/>";print_r($zyll);exit;
        foreach($zyll as $k => $res)
        {
            $pdf->SetWidths(array(24, 24, 24, 30, 22, 22, 22)); // 设置每列的宽度
            $pdf->SetAligns("center");
            $pdf->Row(array("品种", "牌号", "规格", "单价\n(含税,元/吨)", "订货数量\n（吨）", "金额\n(元)","备注")); // 设置列名    

            $pdf->SetFont('simsun', '', 11); //设置字体样式
            $tmoney = 0;
            $tweight = 0;
            foreach($res as $v){
                //输出资源详细信息
                /*if($v['ddid'] == "" || $v['ddid'] == "0"){
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where Dtid    ='".$v['BID']."' and Sid = '".$v['Sid']."' ",0);
                }else{
                    $pickdate = $this->_dao->getOne("select PickUpDate from sm_exc_dd where id    ='".$v['ddid']."'",0);
                }
                ;*/
                $dtjgMoney = $v['BuyQuantity'] * $v['SalesPrice'];
                $tmoney +=$dtjgMoney;
                $tweight +=$v['BuyQuantity'];
                $pdf->SetZeilenhoehe("6");
                $v['VarietyName'] = html_entity_decode($v['VarietyName']);
                $v['MaterialCode'] = html_entity_decode($v['MaterialCode']);
                $v['SpecCode'] = html_entity_decode($v['SpecCode']);
                $v['bz'] = html_entity_decode($v['bz']);
                //$v['OriginCode'] = html_entity_decode($v['OriginCode']);
                //$v['strength'] = html_entity_decode($v['strength']);
                //$v['xincengWeight'] = html_entity_decode($v['xincengWeight']);
                //$pdf->SetAligns("Center");
                $pdf->SetAligns("center");
                $pdf->Row(array($v['VarietyName'], $v['MaterialCode'], $v['SpecCode'], $v['SalesPrice'], $v['BuyQuantity'],$dtjgMoney,$v['bz'])); // 设置列名
            }
            
            $pdf->SetFont('simsun', 'B', 11); //设置字体样式
            $pdf->SetWidths(array(168)); // 设置每列的宽度
        		$pdf->SetWidths(array(168)); // 设置每列的宽度
        		if($tmoney == 0){
        			$tmoney = $contact['TotalMoney'];
        		}
        		$pdf->Row(array('合计人民币金额（大写）'. '   '.$this->cny($tmoney).'        ￥：'.$tmoney)); // 设置列名

            $pdf->Ln();
           
        }
        
        
        
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        if($contact['fjtk']==''){
            $contact['fjtk'] = "无";
        }
        $pdf->Write(9, '说明：');
        addline('', '         '.$contact['fjtk'].'          ', '', $pdf);
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '质量要求及技术标准（以马钢交货标准为准、质保书或技术协议等）：合同标的：高线执行国标GB1499.1-2008，螺纹执行国标GB1499.2-2007,需方无特殊要求。');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '合理损耗标准及计算方法计量：');
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '以国标为准；高线、盘螺以实重交货，高线、盘螺需方验收重量与供方交货计量误差在正负千分之三以内，视为重量无误，螺纹理重交货。');
        

        $pdf->SetMargins(20, 10, 15);

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '二、运输方式及费用：');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10, '（一）款到发货。');

        $pdf->Ln();
        $pdf->Write(10, '（二）本合同交货地点为'.$contact['PickUpState'].$contact['PickUpCity'].$contact['PickUpAddress']);
        
        $pdf->Ln();
        $pdf->Write(10, '（三）运输费用及其它：乙方承担汽运运费（含税）和出库费（含税）。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        /*addline('', date("Y",strtotime($contact['qdtime'])), '年', $pdf);
        addline('', date("m",strtotime($contact['qdtime'])), '月', $pdf);
        addline('', date("d",strtotime($contact['qdtime'])), '日', $pdf);*/
        
        $pdf->Write(10, '三、供货时间：');
        addline('', date("Y",strtotime($zyll[0][0]["CreateDate"])), '年', $pdf);
        addline('', date("m",strtotime($zyll[0][0]["CreateDate"])), '月', $pdf);
        addline('', date("d",strtotime($zyll[0][0]["CreateDate"])), '日', $pdf);
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '四、包装标准、包装物的供应与回收：');

        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'按照质保书要求的包装方式，需方无特殊要求。');
        

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '五、甲方负责向乙方提供产品相对应的质保书。甲方确保质保书随货物一并交付，质保书内容真实、完整和清晰。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '六、验收标准、方法及提出质量异议问题');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（一）乙方按甲方提供的质保书验收货物，验收标准按本合同第二项条款约定。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（二）如发生计量问题，乙方需在收货后 3 日内向甲方提出书面异议资料，并保持货物原样，甲方方可受理，否则认为货物符合本合同约定。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（三）乙方应最迟在收货后 60 日内及时使用加工货物，若有质量异议，乙方需在收货后10日内向甲方提出书面异议资料，并封存原货物，甲方在收到详细书面材料后，及时安排专人到现场解决问题。如有需要，到双方均认可的第三方检验机构进行复检，费用由违约方承担。若货物已全部使用完毕，视为乙方认可货物质量，不得再提出任何质量异议。');

        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '七、结算方式及付款期限');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '1、');
        addline('', date("Y",strtotime($zyll[0][0]["CreateDate"])), '年', $pdf);
        addline('', date("m",strtotime($zyll[0][0]["CreateDate"])), '月', $pdf);
        addline('', date("d",strtotime($zyll[0][0]["CreateDate"])), '日', $pdf);
        addline('', '', '。', $pdf);
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12);
        $pdf->Write(10, '2、定价方式：');
        addline('', '                        ', '', $pdf);
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '八、违约责任');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（一）由于质量问题产生的合理损失由甲方承担。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（二）甲方未按照交货期交货造成乙方损失的由甲方赔偿对应损失，但因生产厂的生产事故或者设备检修等因素造成逾期供货的，甲方将情况通知乙方，并提出解决方案，可免除违约责任，但甲方应全力协调生产尽早交货，或者用替代品解决乙方急需。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（三）若乙方未按合同约定支付当期付货款的，甲方有权解除合同并要求乙方承担由此给甲方造成的一切损失，（如乙方保证金订货，则乙方的订货保证金不予返还）。同时，甲方有权采取以下措施：（1）自行销售乙方所订购的货物；（2）将货物运送至其他仓库，由此产生的额外费用由乙方承担，期间货物毁损、灭失的风险由乙方承担。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（四）甲方按约定送货至指定地点后乙方未能同时收货的，每逾期一日，乙方应当支付');
        addline('', '        ', '', $pdf);
        $pdf->Write(10, '元违约金，超过 3 日仍未收货的，甲方有权解除合同并要求乙方承担由此给甲方造成的一切损失，乙方的订货保证金不予返还。同时，甲方有权采取以下措施：（1）自行销售乙方所订购的货物；（2）将货物运送至其他仓库，由此产生的额外费用由乙方承担，期间货物毁损、灭失的风险由乙方承担。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '九、合同争议解决方式');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); //设置字体样式
        $pdf->Write(10,'本合同在履行过程中发生争议，由双方协商，协商不成，向合同签订地有管辖权的人民法院提起诉讼。');
        
        $pdf->Ln();
        $pdf->SetX(10);
        $pdf->SetFont('simsun', 'B', 12); //设置字体样式
        $pdf->Write(10, '十、其他约定事项');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（一）乙方或乙方委托收货人收货后(乙方或乙方委托人自提完上述全部货物），视为甲方完成本合同的交付义务。本合同所指货物自交付之日起，货物货权与风险一并转移至乙方。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（二）本合同一式肆份，双方各执两份，具有同等法律效力。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（三）双方各自向对方提供营业执照等资料。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（四）合同文本经过修改的，应由双方在修改过的地方盖章确认，否则修改行为无效。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（五）本合同供货的钢材产品仅用于');
        addline('', '                   ', '，不得进入市场流通。', $pdf);
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（六）如乙方需求发生变化，要求变更合同主要条款的，经双方友好协商，可另附补充协议。');
        
        $pdf->Ln();
        $pdf->SetFont('simsun', '', 12); 
        $pdf->Write(10, '（七）本合同自双方签字盖章后生效，合同履行完毕，货款两清后自行废止。');
        
        $pdf->Ln();
        $pdf->SetAligns("center");
        $pdf->SetWidths(array(88,88)); // 设置每列的宽度
        $pdf->SetFont('simsun', '', 11); //设置字体样式
        $pdf->Row(array("甲方", "乙方")); // 设置列名   
        $pdf->Row(array("单位名称：".$salecomp['ComName'], "单位名称：".$buycomp['ComName'])); 
        $pdf->Row(array("单位地址：".$salecomp['Address'], "单位地址：".$buycomp['Address']));
        $pdf->Row(array("营业执照号码：".$salecomp['icnumber'], "营业执照号码：".$buycomp['icnumber']));
        $pdf->Row(array("负责人：".$salecomp['Frdb'], "法定代表人：".$buycomp['Frdb']));
        $pdf->Row(array("委托代理人：".$salecomp['ARealName'], "委托代理人：".$buycomp['ARealName']));
        $pdf->Row(array("电话：".$salecomp['ContactTel'], "电话：".$buycomp['ContactTel']));
        $pdf->Row(array("传真：".$salecomp['ContactFax'], "传真：".$buycomp['ContactFax']));
        $pdf->Row(array("开户银行：".$this->c_substr($salecomp['BankType'],0,12), "开户银行：".$this->c_substr($buycomp['BankType'],0,12)));
        $pdf->Row(array("账号：".$salecomp['BankAccount'], "账号：".$buycomp['BankAccount']));
        $pdf->Row(array("税号：".$salecomp['TaxNo'], "税号：".$buycomp['TaxNo']));
        $pdf->Row(array("邮政编码：".$salecomp['postcode'], "邮政编码：".$buycomp['postcode']));
        
        if($params['fromapp']=='1'){
            echo base64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }else{
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        }
        echo "<link rel='shortcut icon' href='/faviconmg.ico'>";
	}

    public function mg_hetong2($params){

		$hth = $params['hth'];

        include("classfile.php");
        include("phpqrcode.php");

        $contact = $this->_dao->getRow("select * from sm_contract_transaction  where ID = '" . $hth . "' limit 1");
        $PercentInfo = $this->_dao->getRow("select * from mg_KeyResourcePercentInfo limit 1");
        $ZengZhiShui = 1 +($PercentInfo['zengzhi']/100);
        // 资源信息
        $zydetail = $this->_dao->query("select sm_contract_transaction_detail.*,sm_contract_transaction_detail.jhck as jhck2,sm_exc_sales_details.wuliaoNo from sm_contract_transaction_detail,sm_contract_transaction,sm_exc_sales_details where sm_contract_transaction_detail.TID=sm_contract_transaction.ID and sm_contract_transaction_detail.did=sm_exc_sales_details.ID and TID = '" . $hth . "'");

        //print_r($zydetail);
        $zy = $this->_dao->getRow("select * from sm_exc_dd_tag where ID='".$contact['BID']."'");


        if($zy['mg_HeTongFileName']){
            $path = "/usr/local/www/www.isechome.com/".$zy['mg_HeTongFileName'];

            header("Content-type: application/pdf");

            readfile($path);

            echo "<link rel='shortcut icon' href='/faviconmg.ico'>";
            exit;
        }
        

        $ReceiverMid = $contact['ReceiverMid'];
        if($ReceiverMid=="" || $ReceiverMid=="0") $ReceiverMid=$zy['Mid'];
        // 收货公司名称
        $receivercomp = $this->_dao->getRow("select * from sys_company where ID='".$ReceiverMid."'");
                
        // 买方公司名称
        $buycomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Consignee'] . "' ");
        
        
        // 卖方公司名称
        $salecomp = $this->_dao->getRow("SELECT * FROM sys_company WHERE ID='" . $contact['Mid_Shipper'] . "' "); 


        //小计
        $w1=0;
        $w2=0;
        
        foreach($zydetail as $v){
            
            if($v['SalesPrice'])
            $v['SalesPrice'] = round($v['SalesPrice']/($ZengZhiShui),2);

            $Tmoney = round($v['SalesPrice']*$v['BuyQuantity'],2);
                
            $w1 +=$v['BuyQuantity'];
            $w2 +=$Tmoney;

        }

        $num = ceil(count($zydetail)/10);
        


        $pdf = new PDF_Chinese("L","mm", "A4");
        $pdf->AddGBFont();
        $pdf->AddGBFont('simhei', '黑体');
        $pdf->AddGBFont('FZSTK', '方正舒体');
        $pdf->AddGBFont('SIMLI', '隶书');
        $pdf->AddGBFont('simsun', '宋体');
        $pdf->AddGBFont('kaiti', '华文楷体');
        $pdf->Open();

        function addline($txt1, $txt2, $txt3, $pdf)
        {
            $pdf->SetFont('simhei','B', 16);
            $pdf->Write(10, $txt1);
            $pdf->SetFont('simhei', 'U', 16); //设置字体样式
            // $pdf->SetFont('kaiti', 'U', 12); //设置字体样式
            $pdf->Write(10, $txt2);
            $pdf->SetFont('simhei', 'B', 16); //设置字体样式
            $pdf->Write(10, $txt3);
        } 

        for($ii=0;$ii<$num;$ii++){

        
            
        $pdf->AddPage();
        $pdf->SetAutoPageBreak(true,10);

        $pdf->SetDrawColor('214','86','90');
        $pdf->SetTextColor('214','86','90');
        
        $pdf->Image("images/logo2.png",75,5,15,15);
        $pdf->Image("images/mg_htzhang.png",215,33,55,55);
        
        $pdf->SetXY(91,14);
        $pdf->SetFont('kaiti','B',18);
        $pdf->Cell(0,0,'马鞍山钢铁股份有限公司销售公司',0,0,L); 
        
        
        $pdf->SetXY(10,18);
        $pdf->SetFont('simhei', 'B', 12); 
        $pdf->MultiCell(8,7,"签\n订",1,'C');

        $pdf->SetXY(18,18);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(40,7,'日期：',1,0,'L');


        $pdf->SetXY(103,17);
        addline('  ', "     ", '年产品销售合同',$pdf);
        
        $pdf->SetXY(203,18);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(75,7,'合同号：',1,0,'L');
        $pdf->Ln();

        $pdf->SetX(18);
        $pdf->Cell(40,7,'地点：',1,0,'L');
        $pdf->Cell(145,7,'有效期:                                                      履约地: 皖 马鞍山',1,0,'L');
        $pdf->Cell(37.5,7,'分销渠道：',1,0,'L');
        $pdf->Cell(37.5,7,'交货月份：',1,0,'L');
        $pdf->Ln();

        $pdf->SetX(10);
        $pdf->SetFont('simhei', 'B', 8); 
        $pdf->MultiCell(148,5,"合同条款内容：
    1、交货、运输方式和费用负担：卖方代办运输，买方承担运输费用。凡合同配载不足额定车载量也可装运，空载费、运杂费均由买方承担。代办非铁路运输方式，买卖双方另行签订代办运输协议。
    2、检验方式及检验期限：由合同规定的收货方按卖货方质量证明书验收，如发生计量和质量问题需在货到收货单位后10天内向卖方提出详细书面异议资料，并保存原货物，方可受理。
    3、合同变更：变更合同事宜，买方须在交货期前35天提出协商。
    4、买方如延付、拒付货款，卖方有权解除本合同并要求买方承担由此给卖方造成的一切损失。
    5、价格：价格为不含税价，增值税按国家税法规定另收，合同金额为不含税金额。
    6、解决合同纠纷方式：发生争议，买卖双方协商解决，协商不成，向卖方所在地人民法院起诉。
    7、违约责任：按《中华人民共和国合同法》及有关规定执行。
    8、结算方式及付款期限：除买卖双方另有约定外，买方须全额支付预付款。",1,'L');
        $pdf->Ln();
        
        
        
        $pdf->SetXY(160,38);
        $pdf->SetFont('simhei', 'B', 12); 
        $pdf->MultiCell(10,7,"卖\n方\n单\n位",0,'L');


        $pdf->SetXY(168,32);
        $pdf->SetFont('simhei', 'B', 11); 
        $pdf->Cell(168,8,'名称：马鞍山钢铁股份有限公司销售公司',0,0,'L');
        
        $pdf->SetXY(168,59);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(168,8,'传真：0555-2886199  2883734',0,0,'L');

        $pdf->SetXY(168,64);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(168,8,'销售服务中心电话：0555-2877325',0,0,'L');

        $pdf->SetXY(227,62);
        $pdf->SetFont('simhei', 'B', 12); 
        $pdf->Cell(168,8,'卖方代理人：',0,0,'L');

        $pdf->SetXY(168,32);
        $pdf->SetWidths(array(110)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("4");
        $pdf->SetFont('simhei', '', 9); 
        $pdf->SetAligns("L");
        $pdf->Row(array('

地址：安徽省马鞍山市九华西路8号       邮编：243003
统一社会信用代码：91340000610400837Y
开户行：马鞍山市工行马钢支行          帐号：1306020409001211886
开户行：马鞍山市建行冶金专业支行      帐号：34001658808050330842
开户行：马鞍山市中行马钢支行          帐号：182706214990
    
    
    '));
        $pdf->Ln();
        
        $pdf->SetXY(158,72);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(10,15,'',1,0,'C');
        $pdf->SetXY(158,75);
        $pdf->MultiCell(10,5,"买方\n单位",0,'L');

        $pdf->SetXY(168,72);
        $pdf->Cell(55,7.5,'名称：',1,0,'L');
        $pdf->Cell(55,7.5,'编码：',1,0,'L');
        $pdf->Ln();

        $pdf->SetX(168);
        $pdf->Cell(55,7.5,'地址：',1,0,'L');
        $pdf->Cell(55,7.5,'代理人：',1,0,'L');
        $pdf->Ln();

        
        $pdf->SetXY(10,89);
        $pdf->SetFont('simhei', 'B', 8); //设置字体样式

        $pdf->Cell(7,6,'序',1,0,'C');
        $pdf->Cell(23,6,'品    名',1,0,'C');
        $pdf->Cell(23,6,'规    格',1,0,'C');
        $pdf->Cell(23,6,'物 料 号',1,0,'C');
        $pdf->Cell(23,6,'价格(元/吨)',1,0,'C');
        $pdf->Cell(23,6,'订货数量(吨)',1,0,'C');
        $pdf->Cell(26,6,'金 额(元)',1,0,'C');
        $pdf->Cell(28,6,'执行标准',1,0,'C');
        $pdf->Cell(92,6,'说    明',1,0,'C');
            
        
        $pdf->Ln();
            
        //小计
        $noid = 0;

        for($i=0;$i<10;$i++){
        
            $noid++;
            //输出资源详细信息$w1=0;
            $pdf->SetX(10);
            $pdf->SetLeftMargin(10);
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simhei', '', 8); 
            $pdf->SetAligns("C","C");

            $pdf->SetWidths(array(7,23,23,23,23,23,26,28,92)); // 设置每列的宽度
            $pdf->Row(array($noid,"","","","","","","","")); 

        }
                
                    

        $pdf->SetX(10);
        $pdf->SetWidths(array(7,23,23,23,23,23,26,28,92)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simhei', 'B', 7); 
        $pdf->SetAligns("C");
        $pdf->Row(array('小计','','','','','','','','')); 
        $pdf->Ln();

        $pdf->SetXY(10,161);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(7,24,'',1,0,'C');
        $pdf->SetXY(10,165);
        $pdf->MultiCell(7,4,"收\n货\n单\n位",0,'C');

        $pdf->SetXY(17,161);
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->SetWidths(array(90,51)); // 设置每列的宽度
        $pdf->SetAligns("L");
        
        $pdf->Row(array('全称：','编码：')); 
        $pdf->SetXY(17,167);
        $pdf->Row(array('地址：','邮编：')); 
        $pdf->SetXY(17,173);
        $pdf->Row(array('到站(港)：','电话：')); 
        $pdf->SetXY(17,179);
        $pdf->Row(array('专用线：','生产厂：')); 

        $pdf->SetWidths(array(148)); // 设置每列的宽度
        $pdf->Row(array('补充说明：')); 


        $pdf->SetXY(158,161);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(9,30,'',1,0,'C');
        $pdf->SetXY(158,165);
        $pdf->MultiCell(9,5,"付\n款\n单\n位",0,'C');

        $pdf->SetXY(167,161);
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->SetWidths(array(70,41)); // 设置每列的宽度
        $pdf->SetAligns("L");
        
        $pdf->Row(array('全称：','编码：')); 
        $pdf->SetXY(167,167);
        $pdf->Row(array('地址：','邮编：')); 
        $pdf->SetXY(167,173);
        $pdf->Row(array('帐号：','电话：')); 
        $pdf->SetXY(167,179);
        $pdf->Row(array('开户行：','传真：')); 
        

        $pdf->SetXY(167,185);
        $pdf->SetWidths(array(111)); // 设置每列的宽度
        $pdf->Row(array('纳税人登记号：')); 


            
        $pdf->SetXY(10,190);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->SetX(10);
        $pdf->Write(10,'打印日期：');
        $pdf->Ln();

        //合同内容
        //=========================================================
        $pdf->SetTextColor('80');

        $pdf->SetXY(30,18);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(45,7,date("Y-m-d",strtotime($zy['CreateDate'])),0,0,'L');

        
        $pdf->SetXY(215,18);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(75,7,$zy['mg_dd'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(112,18);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(75,7,date("Y"),0,0,'L');
        $pdf->Ln();

        $pdf->SetX(30);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(45,7,'马鞍山',0,0,'L');
        $pdf->SetX(72);
        $pdf->Cell(147,7,date("Ym",strtotime($zy['CreateDate'])),0,0,'L');

        /*if($zy['mg_factory']!="0"){
            $pdf->Cell(37.5,7,'存储销售(厂发)',0,0,'L');
        }else{
            $pdf->Cell(37.5,7,'存储销售(本地)',0,0,'L');
        }*/
        $pdf->SetFont('simhei', 'B', 9); //设置字体样式
        $pdf->Cell(37.5,7,"厂内直发(现货中心)",0,0,'L');
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Cell(37.5,7,date("Ym",strtotime($zy['CreateDate'])),0,0,'L');
        $pdf->Ln();

        

        $pdf->SetXY(178,72);
        $pdf->SetFont('simhei', 'B', 10); 
        $pdf->Cell(55,7.5,$buycomp['ComName'],0,0,'L');
        $pdf->Cell(45,7.5,ltrim($buycomp['mg_id']),0,0,'L');
        $pdf->Ln();

        $pdf->SetX(178);
        $pdf->Cell(58,7.5,$buycomp['Address'],0,0,'L');
        $pdf->Cell(55,7.5,'',0,0,'L');
        $pdf->Ln();


        $pdf->SetXY(10,95);
        $pdf->SetFont('simhei', 'B', 9); //设置字体样式
            
        //小计
        //$w1=0;
        //$w2=0;
        
        $noid = 0;
        
        for($i=0;$i<10;$i++){
            $noid++;
            $k = $i+($ii*10);
            $v = $zydetail[$k];
            
            if($v['SalesPrice'])
            $v['SalesPrice'] = round($v['SalesPrice']/($ZengZhiShui),2);

            $Tmoney='';
            $biaozhun='';
            $shuoming='';
            if($v['BuyQuantity']!=""){
                $Tmoney = round($v['SalesPrice']*$v['BuyQuantity'],2);
                
                if( $v['Vid']==$GLOBALS['PANLUO_VID'] || $v['Vid']==$GLOBALS["LWG_VID"]){
                    $biaozhun = "GB/T1499.2-2018";

                    $Type = substr($v['wuliaoNo'], -1);
                    if ($Type == 'D') {
                        $shuoming = " 单定尺 ".$v['kd'].".00;";
                    }
                    //$shuoming .= $v['kd'].".00;";
                    
                }else{
                    $biaozhun = "GB/T1499.1-2017";


                    
                    $count3 = substr_count($v['wuliaoNo'], '-A');
                    if($count3>0){
                        $shuoming .=" 用途：拉丝；";

                    }
                }

                $count2 = substr_count($v['wuliaoNo'], '-B');
                if($count2>0){
                    $shuoming .=" 用途：建筑；";

                }

                
                
            }

            //输出资源详细信息$w1=0;
            $pdf->SetX(10);
            $pdf->SetLeftMargin(10);
            $pdf->SetZeilenhoehe("6");
            $pdf->SetFont('simhei', '', 9); 
            $pdf->SetAligns("C","C");

                
            $pdf->SetWidths(array(7,23,23,23,23,23,26,28,92)); // 设置每列的宽度
            $pdf->Row(array('',$v['VarietyName'],$v['SpecCode'],$v['MaterialCode'],$v['SalesPrice'],$v['BuyQuantity'],$Tmoney,$biaozhun,$shuoming)); 
                
            //$w1 +=$v['BuyQuantity'];
            //$w2 +=$Tmoney;

        }
                    

        $pdf->SetX(10);
        $pdf->SetWidths(array(7,23,23,23,23,23,26,28,92)); // 设置每列的宽度
        $pdf->SetZeilenhoehe("6");
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->SetAligns("C");
        $pdf->Row(array('','','','','',number_format($w1),number_format($w2),'','')); 
        $pdf->Ln();

        $pdf->SetXY(27,160.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(89,7,$receivercomp['ComName'],0,0,'L');
        $pdf->Cell(45,7,ltrim($receivercomp['mg_id']),0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(27,166.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(89,7,$receivercomp['Address'],0,0,'L');
        $pdf->Cell(45,7,$receivercomp['postcode'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(34,172.5);
        $pdf->SetFont('simhei', 'B', 9); 
        if($zy['daozhancangku']==""){
            $pdf->Cell(82,7,'自提',0,0,'L');
        }else{
            $pdf->Cell(82,7,$zy['daozhancangku'],0,0,'L');
        }
        $pdf->Cell(37,7,$receivercomp['ContactTel'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(34,178.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(85,7,$zydetail[0]['jhck2'],0,0,'L');
        $pdf->Cell(37,7,'销售公司工厂',0,0,'L');
        $pdf->Ln();




        $pdf->SetXY(176,160.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(70,7,$buycomp['ComName'],0,0,'L');
        $pdf->Cell(45,7,ltrim($buycomp['mg_id']),0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(176,166.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(70,7,$buycomp['Address'],0,0,'L');
        $pdf->Cell(45,7,$buycomp['postcode'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(180,172.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(66,7,$buycomp['BankAccount'],0,0,'L');
        $pdf->Cell(45,7,$buycomp['ContactTel'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(180,178.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(66,7,$buycomp['BankType'],0,0,'L');
        $pdf->Cell(45,7,$buycomp['ContactFax'],0,0,'L');
        $pdf->Ln();

        $pdf->SetXY(190,184.5);
        $pdf->SetFont('simhei', 'B', 9); 
        $pdf->Cell(85,7,$buycomp['TaxNo'],0,0,'L');
        $pdf->Ln();
        

        $pdf->SetXY(30,190);
        $pdf->SetFont('simhei', 'B', 10); //设置字体样式
        $pdf->Write(10,date("Y-m-d"));
        $pdf->Ln();
        //=========================================================

        }

        
        if($params['fromapp']=='1'){
            echo base64_encode($pdf->Output($contact['ContractNo'] . '.pdf', 'S')); //D or I
        }else if($params['save']=='1'){
            $path = "uploadfile/mg_hetong/".date("YmdHis")."_".$zy['ID'] . ".pdf";
            $pdf->Output("/usr/local/www/www.isechome.com/".$path, 'F');

            $this->_dao->execute("update  sm_exc_dd_tag set mg_HeTongFileName='".$path."' where ID='".$zy['ID']."'");
        }else{
            $pdf->Output($contact['ContractNo'] . '.pdf', 'I'); //D or I
        }
        echo "<link rel='shortcut icon' href='/faviconmg.ico'>";
	}
} 

?>