<?php
/**
 * 新的中文PDF支持类
 * 基于mPDF库，支持PHP 8.1和UTF-8编码
 * 保持与原有PDF_Chinese类的接口兼容性
 */

require_once __DIR__ . '/UniversalPdfGenerator.php';
require_once __DIR__ . '/PdfAdapter.php';

/**
 * 增强的PDF_Chinese类
 * 继承UniversalPdfGenerator，添加原有系统需要的特定功能
 */
class PDF_Chinese_Enhanced extends UniversalPdfGenerator
{
    // 表格相关属性
    private $widths = [];
    private $height = 6;
    private $aligns = [];
    private $valigns = [];
    private $fills = [];
    private $innerBorderX = true;
    private $innerBorderY = true;
    private $doBorder = true;
    private $doCalculateHeight = true;
    private $zeilenhoehe = 4.5;
    
    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        parent::__construct($orientation, $unit, $size);
    }
    
    /**
     * 兼容原有的AddCIDFont方法
     */
    public function AddCIDFont($family, $style, $name, $cw, $CMap, $registry)
    {
        // mPDF自动处理CID字体，这里保持兼容性
        return true;
    }
    
    /**
     * 兼容原有的AddGBFont方法（带参数版本）
     */
    public function AddGBFont($family = '', $name = '')
    {
        if (!empty($family) && !empty($name)) {
            // 记录字体映射，但实际由mPDF处理
            $this->fontMap[$family] = PdfAdapter::mapFontName($name);
        }
        return parent::AddGBFont($family, $name);
    }
    
    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->widths = $widths;
        parent::SetWidths($widths);
    }
    
    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        $this->aligns = is_array($aligns) ? $aligns : [$aligns];
        parent::SetAligns($aligns);
    }
    
    /**
     * 设置垂直对齐方式
     */
    public function SetValigns($valigns)
    {
        $this->valigns = is_array($valigns) ? $valigns : [$valigns];
    }
    
    /**
     * 设置填充色
     */
    public function SetFills($fills)
    {
        $this->fills = is_array($fills) ? $fills : [$fills];
    }
    
    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->zeilenhoehe = $height;
        $this->height = $height;
        parent::SetZeilenhoehe($height);
    }
    
    /**
     * 增强的表格行绘制
     */
    public function Row($data)
    {
        // 转换编码
        $convertedData = [];
        foreach ($data as $item) {
            $convertedData[] = PdfAdapter::convertEncoding($item);
        }
        
        parent::Row($convertedData);
    }
    
    /**
     * 兼容原有的Cell方法
     */
    public function Cell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        // 转换编码
        $txt = PdfAdapter::convertEncoding($txt);
        
        // 使用HTML表格模拟Cell功能
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }
        
        $alignStyle = '';
        if ($align) {
            $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right'];
            $alignStyle = 'text-align: ' . (isset($alignMap[$align]) ? $alignMap[$align] : 'left') . ';';
        }
        
        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }
        
        $html = '<table style="width: ' . $w . 'mm; height: ' . $h . 'mm; border-collapse: collapse;">';
        $html .= '<tr><td style="' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px;">';
        $html .= htmlspecialchars($txt, ENT_QUOTES, 'UTF-8');
        $html .= '</td></tr></table>';
        
        $this->mpdf->WriteHTML($html);
        
        if ($ln == 1) {
            $this->Ln($h);
        } elseif ($ln == 2) {
            $this->SetXY($this->leftMargin, $this->GetY() + $h);
        }
    }
    
    /**
     * 多字节字符串写入
     */
    public function MBWrite($h, $txt, $link = '')
    {
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);
        return $this->Write($h, $txt, $link);
    }
    
    /**
     * 多字节字符串单元格
     */
    public function MBCell($w, $h = 0, $txt = '', $border = 0, $ln = 0, $align = '', $fill = false, $link = '')
    {
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);
        return $this->Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }
    
    /**
     * 多字节字符串多行输出
     */
    public function MBMultiCell($w, $h, $txt, $border = 0, $align = 'J', $fill = false)
    {
        // 确保文本是UTF-8编码
        $txt = PdfAdapter::convertEncoding($txt);
        
        // 使用HTML实现多行文本
        $borderStyle = '';
        if ($border) {
            $borderStyle = 'border: 1px solid #000;';
        }
        
        $alignStyle = '';
        $alignMap = ['L' => 'left', 'C' => 'center', 'R' => 'right', 'J' => 'justify'];
        if (isset($alignMap[$align])) {
            $alignStyle = 'text-align: ' . $alignMap[$align] . ';';
        }
        
        $fillStyle = '';
        if ($fill) {
            $fillStyle = 'background-color: #f0f0f0;';
        }
        
        $html = '<div style="width: ' . $w . 'mm; ' . $borderStyle . $alignStyle . $fillStyle . 'padding: 2px; line-height: ' . $h . 'mm;">';
        $html .= nl2br(htmlspecialchars($txt, ENT_QUOTES, 'UTF-8'));
        $html .= '</div>';
        
        $this->mpdf->WriteHTML($html);
    }
    
    /**
     * 获取字符串宽度
     */
    public function GetStringWidth($s)
    {
        $s = PdfAdapter::convertEncoding($s);
        return $this->mpdf->GetStringWidth($s);
    }
    
    /**
     * 设置文本颜色
     */
    public function SetTextColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            // 灰度模式
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            // RGB模式
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetTextColor($color);
    }
    
    /**
     * 设置绘图颜色
     */
    public function SetDrawColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetDrawColor($color);
    }
    
    /**
     * 设置填充颜色
     */
    public function SetFillColor($r, $g = null, $b = null)
    {
        if ($g === null && $b === null) {
            $color = sprintf('#%02x%02x%02x', $r, $r, $r);
        } else {
            $color = sprintf('#%02x%02x%02x', $r, $g, $b);
        }
        $this->mpdf->SetFillColor($color);
    }
    
    /**
     * 画矩形
     */
    public function Rect($x, $y, $w, $h, $style = '')
    {
        $this->mpdf->Rect($x, $y, $w, $h, $style);
    }
    
    /**
     * 设置页边距
     */
    public function SetMargins($left, $top, $right = null)
    {
        if ($right === null) {
            $right = $left;
        }
        $this->leftMargin = $left;
        $this->topMargin = $top;
        $this->rightMargin = $right;
        $this->mpdf->SetMargins($left, $right, $top);
    }
    
    /**
     * 设置左边距
     */
    public function SetLeftMargin($margin)
    {
        $this->leftMargin = $margin;
        $this->mpdf->SetLeftMargin($margin);
    }
    
    /**
     * 设置上边距
     */
    public function SetTopMargin($margin)
    {
        $this->topMargin = $margin;
        $this->mpdf->SetTopMargin($margin);
    }
    
    /**
     * 设置右边距
     */
    public function SetRightMargin($margin)
    {
        $this->rightMargin = $margin;
        $this->mpdf->SetRightMargin($margin);
    }
}

// 为了保持完全兼容，创建别名
class PDF_Chinese extends PDF_Chinese_Enhanced
{
    // 继承所有功能，保持原有类名
}
