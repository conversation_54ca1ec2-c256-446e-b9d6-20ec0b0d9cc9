<?php
/**
 * 通用PDF生成器类
 * 用于替换原有的FPDF库，支持PHP 8.1和UTF-8编码
 * 兼容原有的PDF_Chinese类接口
 */

require_once __DIR__ . '/vendor/autoload.php'; // 需要先安装mPDF

use Mpdf\Mpdf;
use Mpdf\Config\ConfigVariables;
use Mpdf\Config\FontVariables;

class UniversalPdfGenerator
{
    private $mpdf;
    private $currentX = 0;
    private $currentY = 0;
    private $pageWidth = 210; // A4宽度(mm)
    private $pageHeight = 297; // A4高度(mm)
    private $leftMargin = 10;
    private $topMargin = 10;
    private $rightMargin = 10;
    private $bottomMargin = 10;
    private $currentFont = 'simsun';
    private $currentFontSize = 12;
    private $currentFontStyle = '';
    private $lineHeight = 5;
    
    // 表格相关属性
    private $tableWidths = [];
    private $tableAligns = [];
    private $tableHeight = 6;
    
    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        // 配置中文字体
        $defaultConfig = (new ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        
        $defaultFontConfig = (new FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];
        
        // 添加中文字体配置
        $fontData = $fontData + [
            'simsun' => [
                'R' => 'simsun.ttf',
                'B' => 'simsun.ttf',
            ],
            'simhei' => [
                'R' => 'simhei.ttf',
                'B' => 'simhei.ttf',
            ],
            'kaiti' => [
                'R' => 'kaiti.ttf',
                'B' => 'kaiti.ttf',
            ],
        ];
        
        $config = [
            'fontDir' => array_merge($fontDirs, [__DIR__ . '/fonts']),
            'fontdata' => $fontData,
            'default_font' => 'simsun',
            'orientation' => $orientation,
            'format' => $size,
            'margin_left' => $this->leftMargin,
            'margin_right' => $this->rightMargin,
            'margin_top' => $this->topMargin,
            'margin_bottom' => $this->bottomMargin,
        ];
        
        $this->mpdf = new Mpdf($config);
        $this->mpdf->SetAutoFont();
    }
    
    /**
     * 兼容原有的AddGBFont方法
     */
    public function AddGBFont($family = '', $name = '')
    {
        // mPDF自动处理中文字体，这里保持兼容性
        return true;
    }
    
    /**
     * 兼容原有的Open方法
     */
    public function Open()
    {
        // mPDF自动处理，保持兼容性
        return true;
    }
    
    /**
     * 添加页面
     */
    public function AddPage($orientation = '', $size = '')
    {
        if ($this->mpdf->page > 0) {
            $this->mpdf->AddPage($orientation, '', '', '', '', 
                $this->leftMargin, $this->rightMargin, 
                $this->topMargin, $this->bottomMargin);
        }
        $this->currentX = $this->leftMargin;
        $this->currentY = $this->topMargin;
    }
    
    /**
     * 设置字体
     */
    public function SetFont($family, $style = '', $size = 0)
    {
        $this->currentFont = $family;
        $this->currentFontStyle = $style;
        if ($size > 0) {
            $this->currentFontSize = $size;
        }
        
        // 映射字体名称
        $fontMap = [
            'GB' => 'simsun',
            'simsun' => 'simsun',
            'simhei' => 'simhei',
            'kaiti' => 'kaiti',
            'FZSTK' => 'kaiti',
            'SIMLI' => 'kaiti',
        ];
        
        $mappedFont = isset($fontMap[$family]) ? $fontMap[$family] : 'simsun';
        $this->mpdf->SetFont($mappedFont, $style, $this->currentFontSize);
    }
    
    /**
     * 设置位置
     */
    public function SetX($x)
    {
        $this->currentX = $x;
    }
    
    public function SetY($y)
    {
        $this->currentY = $y;
    }
    
    public function SetXY($x, $y)
    {
        $this->currentX = $x;
        $this->currentY = $y;
    }
    
    /**
     * 获取当前位置
     */
    public function GetX()
    {
        return $this->currentX;
    }
    
    public function GetY()
    {
        return $this->currentY;
    }
    
    /**
     * 写入文本
     */
    public function Write($h, $txt, $link = '')
    {
        $this->lineHeight = $h;
        
        // 转换编码为UTF-8
        if (!mb_check_encoding($txt, 'UTF-8')) {
            $txt = mb_convert_encoding($txt, 'UTF-8', 'GBK');
        }
        
        // 计算文本宽度
        $textWidth = $this->mpdf->GetStringWidth($txt);
        
        // 使用WriteText方法写入文本
        $this->mpdf->WriteText($this->currentX, $this->currentY, $txt);
        
        // 更新当前位置
        $this->currentX += $textWidth;
    }
    
    /**
     * 换行
     */
    public function Ln($h = null)
    {
        if ($h === null) {
            $h = $this->lineHeight;
        }
        $this->currentX = $this->leftMargin;
        $this->currentY += $h;
    }
    
    /**
     * 画线
     */
    public function Line($x1, $y1, $x2, $y2)
    {
        $this->mpdf->Line($x1, $y1, $x2, $y2);
    }
    
    /**
     * 插入图片
     */
    public function Image($file, $x = null, $y = null, $w = 0, $h = 0, $type = '', $link = '')
    {
        if ($x === null) $x = $this->currentX;
        if ($y === null) $y = $this->currentY;
        
        $this->mpdf->Image($file, $x, $y, $w, $h, $type, $link);
    }
    
    /**
     * 设置表格列宽
     */
    public function SetWidths($widths)
    {
        $this->tableWidths = $widths;
    }
    
    /**
     * 设置表格对齐方式
     */
    public function SetAligns($aligns)
    {
        if (is_string($aligns)) {
            // 如果是字符串，转换为数组
            $alignArray = [];
            for ($i = 0; $i < count($this->tableWidths); $i++) {
                $alignArray[] = $aligns;
            }
            $this->tableAligns = $alignArray;
        } else {
            $this->tableAligns = $aligns;
        }
    }
    
    /**
     * 设置行高
     */
    public function SetZeilenhoehe($height)
    {
        $this->tableHeight = $height;
    }
    
    /**
     * 绘制表格行
     */
    public function Row($data)
    {
        if (empty($this->tableWidths)) {
            return;
        }
        
        $html = '<table style="width: 100%; border-collapse: collapse;">';
        $html .= '<tr>';
        
        for ($i = 0; $i < count($data); $i++) {
            $width = isset($this->tableWidths[$i]) ? $this->tableWidths[$i] : 20;
            $align = isset($this->tableAligns[$i]) ? strtolower($this->tableAligns[$i]) : 'left';
            
            if ($align === 'c' || $align === 'center') {
                $align = 'center';
            } elseif ($align === 'r' || $align === 'right') {
                $align = 'right';
            } else {
                $align = 'left';
            }
            
            $cellData = isset($data[$i]) ? $data[$i] : '';
            
            // 转换编码
            if (!mb_check_encoding($cellData, 'UTF-8')) {
                $cellData = mb_convert_encoding($cellData, 'UTF-8', 'GBK');
            }
            
            $html .= '<td style="width: ' . $width . 'mm; text-align: ' . $align . '; border: 1px solid #000; padding: 2px; height: ' . $this->tableHeight . 'mm;">';
            $html .= htmlspecialchars($cellData, ENT_QUOTES, 'UTF-8');
            $html .= '</td>';
        }
        
        $html .= '</tr>';
        $html .= '</table>';
        
        $this->mpdf->WriteHTML($html);
        $this->currentY += $this->tableHeight;
    }
    
    /**
     * 输出PDF
     */
    public function Output($name = '', $dest = 'I')
    {
        return $this->mpdf->Output($name, $dest);
    }
    
    /**
     * 设置自动分页
     */
    public function SetAutoPageBreak($auto, $margin = 0)
    {
        // mPDF自动处理分页
        return true;
    }
}

/**
 * 兼容类，继承UniversalPdfGenerator以保持向后兼容
 */
class PDF_Chinese extends UniversalPdfGenerator
{
    public function __construct($orientation = 'P', $unit = 'mm', $size = 'A4')
    {
        parent::__construct($orientation, $unit, $size);
    }
}
