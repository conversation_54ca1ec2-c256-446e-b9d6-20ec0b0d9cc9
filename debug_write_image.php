<?php
/**
 * 调试Write和Image方法
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

// 模拟addline函数
function addline($txt1, $txt2, $txt3, $pdf)
{
    echo "addline调用: '$txt1', '$txt2', '$txt3'\n";
    $pdf->SetFont('simsun','', 12);
    $pdf->Write(10, $txt1);
    $pdf->SetFont('kaiti', 'U', 12);
    $pdf->Write(10, $txt2);
    $pdf->SetFont('simsun', '', 12);
    $pdf->Write(10, $txt3);
}

try {
    echo "开始调试Write和Image方法...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    $pdf->AddGBFont('kaiti', '华文楷体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 创建测试图片
    $testImagePath = 'debug_image.png';
    if (!file_exists($testImagePath)) {
        $image = imagecreate(20, 20);
        $bg_color = imagecolorallocate($image, 255, 0, 0);
        imagepng($image, $testImagePath);
        imagedestroy($image);
        echo "测试图片创建成功！\n";
    }
    
    echo "\n=== 测试1: 基本Write方法 ===\n";
    $pdf->SetFont('simsun', '', 12);
    $pdf->Write(10, '开始');
    echo "写入'开始'后\n";
    $pdf->Write(10, '中间');
    echo "写入'中间'后\n";
    $pdf->Write(10, '结束');
    echo "写入'结束'后\n";
    $pdf->Ln();
    
    echo "\n=== 测试2: addline函数 ===\n";
    addline('', '2024', '年', $pdf);
    echo "addline调用后\n";
    addline('', '12', '月', $pdf);
    echo "第二次addline调用后\n";
    $pdf->Ln();
    
    echo "\n=== 测试3: Image方法 ===\n";
    $pdf->Write(10, '图片前');
    echo "写入'图片前'后\n";
    $pdf->Image($testImagePath, 100, 50, 15, 15);
    echo "插入图片后\n";
    $pdf->Write(10, '图片后');
    echo "写入'图片后'后\n";
    $pdf->Ln();
    
    echo "\n=== 测试4: 空字符串 ===\n";
    $pdf->Write(10, '前面');
    echo "写入'前面'后\n";
    $pdf->Write(10, '');
    echo "写入空字符串后\n";
    $pdf->Write(10, '   ');
    echo "写入空格后\n";
    $pdf->Write(10, '后面');
    echo "写入'后面'后\n";
    
    // 输出PDF
    $pdf->Output('debug_write_image.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: debug_write_image.pdf\n";
    echo "请检查PDF文件，看看哪里出现了意外换行。\n";
    
    // 清理测试图片
    if (file_exists($testImagePath)) {
        unlink($testImagePath);
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
