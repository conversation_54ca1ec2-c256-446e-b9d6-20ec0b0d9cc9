<?php
/**
 * 测试最终修复：空字符串和图片换行问题
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

try {
    echo "开始测试最终修复...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    // 创建测试图片
    $testImagePath = 'test_final.png';
    if (!file_exists($testImagePath)) {
        $image = imagecreate(20, 20);
        $bg_color = imagecolorallocate($image, 0, 255, 0); // 绿色背景
        imagepng($image, $testImagePath);
        imagedestroy($image);
        echo "测试图片创建成功！\n";
    }
    
    // 设置字体
    $pdf->SetFont('simsun', '', 12);
    
    echo "测试1: 空字符串是否换行\n";
    $pdf->Write(10, '开始文本');
    $pdf->Write(10, '   '); // 空格字符串
    $pdf->Write(10, ''); // 空字符串
    $pdf->Write(10, '结束文本');
    $pdf->Ln(); // 手动换行
    
    echo "测试2: 图片插入是否换行\n";
    $pdf->Write(10, '图片前文本');
    
    // 记录位置
    $beforeX = $pdf->GetX();
    $beforeY = $pdf->GetY();
    echo "插入图片前位置: X={$beforeX}, Y={$beforeY}\n";
    
    // 插入图片
    $pdf->Image($testImagePath, 100, $beforeY - 5, 15, 15);
    
    // 检查位置
    $afterX = $pdf->GetX();
    $afterY = $pdf->GetY();
    echo "插入图片后位置: X={$afterX}, Y={$afterY}\n";
    
    // 继续写入文本
    $pdf->Write(10, '图片后文本');
    $pdf->Ln();
    
    echo "测试3: 模拟addline函数调用\n";
    // 模拟addline('', '2024', '年', $pdf)的调用
    $pdf->Write(10, ''); // 空字符串
    $pdf->Write(10, '2024'); // 实际内容
    $pdf->Write(10, '年'); // 后缀
    $pdf->Ln();
    
    // 输出PDF
    $pdf->Output('test_final_fix.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: test_final_fix.pdf\n";
    
    // 检查结果
    if ($beforeX == $afterX && $beforeY == $afterY) {
        echo "✓ 图片插入没有影响位置！\n";
    } else {
        echo "✗ 图片插入仍然影响位置\n";
    }
    
    echo "请检查PDF文件，确认：\n";
    echo "1. 空字符串不会导致换行\n";
    echo "2. 图片插入不会导致换行\n";
    echo "3. 文本应该连续显示\n";
    
    // 清理测试图片
    if (file_exists($testImagePath)) {
        unlink($testImagePath);
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
