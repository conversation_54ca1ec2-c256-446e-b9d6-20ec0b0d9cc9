<?php
/**
 * 测试HTML缓冲区方法
 */

// 模拟系统环境
define('LIB_DIR', __DIR__ . '/libs');

// 引入新的PDF系统
require_once 'www.zgdzwz.com/chinese.php';

// 模拟addline函数
function addline($txt1, $txt2, $txt3, $pdf)
{
    $pdf->SetFont('simsun','', 12);
    $pdf->Write(10, $txt1);
    $pdf->SetFont('kaiti', 'U', 12);
    $pdf->Write(10, $txt2);
    $pdf->SetFont('simsun', '', 12);
    $pdf->Write(10, $txt3);
}

try {
    echo "开始测试HTML缓冲区方法...\n";
    
    // 创建PDF实例
    $pdf = new PDF_Chinese();
    $pdf->AddGBFont();
    $pdf->AddGBFont('simsun', '宋体');
    $pdf->AddGBFont('kaiti', '华文楷体');
    
    $pdf->Open();
    $pdf->AddPage();
    
    echo "测试1: 基本Write方法\n";
    $pdf->SetFont('simsun', '', 12);
    $pdf->Write(10, '第一段');
    $pdf->Write(10, '第二段');
    $pdf->Write(10, '第三段');
    $pdf->Ln(); // 这里会刷新缓冲区
    
    echo "测试2: addline函数\n";
    addline('', '2024', '年', $pdf);
    $pdf->Ln();
    addline('', '12', '月', $pdf);
    $pdf->Ln();
    
    echo "测试3: 空字符串处理\n";
    $pdf->Write(10, '前面');
    $pdf->Write(10, ''); // 空字符串，应该被忽略
    $pdf->Write(10, '   '); // 空格，应该被忽略
    $pdf->Write(10, '后面');
    $pdf->Ln();
    
    echo "测试4: 中文字符\n";
    $pdf->Write(10, '合计人民币金额');
    $pdf->Write(10, '（大写）：');
    $pdf->Write(10, '壹万贰仟叁佰肆拾伍元陆角柒分');
    $pdf->Ln();
    
    // 输出PDF
    $pdf->Output('test_buffer_method.pdf', 'F');
    
    echo "\nPDF生成成功！文件保存为: test_buffer_method.pdf\n";
    echo "请检查PDF文件，确认：\n";
    echo "1. 同行文本连续显示，没有意外换行\n";
    echo "2. Ln()正确换行\n";
    echo "3. 中文字符正常显示\n";
    echo "4. addline函数正常工作\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
}
?>
